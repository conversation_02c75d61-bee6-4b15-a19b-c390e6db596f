package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.amqp.RabbitHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.activity.restclient.ActmonRestClient;
import com.emc.it.eis.activity.test.AbstractRestClientRMQTest;

@SpringJUnitConfig(locations = "classpath:/com/emc/it/eis/activity/ActivityInterceptorRestBeanWrapperTests-context.xml")
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"})
@EnableAutoConfiguration(exclude = {RabbitHealthContributorAutoConfiguration.class, DataSourceAutoConfiguration.class, ActiveMQAutoConfiguration.class, RabbitAutoConfiguration.class, KafkaAutoConfiguration.class})
@ActiveProfiles("test")
class RestClientPropertiesTest
{
	@Autowired
	ActmonRestClient restClient;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER")	
	OAuth2AuthorizedClientManager authorizedClientManager;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_OAUTH2")
	RestTemplate restTemplate;

	@Test
	void test() {
		assertNotNull(restClient.getRestTemplate());
		assertNotNull(restClient.getClientProperties());
		assertEquals("http://localhost:8080/cic-activity-monitor-webservice/actmon.ws",
				restClient.getClientProperties().getUrl());
		assertEquals("", restClient.getClientProperties().getApiKey());
		assertEquals(0, restClient.getClientProperties().getApiKey().length());
		assertFalse(restClient.getClientProperties().getUrl().contains("https"));
	}

}

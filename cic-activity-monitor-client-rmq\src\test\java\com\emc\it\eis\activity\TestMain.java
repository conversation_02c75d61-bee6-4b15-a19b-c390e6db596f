package com.emc.it.eis.activity;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;

import jakarta.xml.bind.Marshaller;

public class TestMain {
	private static final Logger logger = LoggerFactory.getLogger(TestMain.class);
	private static Jaxb2Marshaller payloadContextMarshaller;

	private static void createPcMarshaller() throws Exception {
		payloadContextMarshaller = new Jaxb2Marshaller();
		payloadContextMarshaller.setContextPath("com.emc.it.enterprise.msg.v1");
		Map<String, Object> props = new HashMap<>();
		props.put(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
		payloadContextMarshaller.setMarshallerProperties(props);
		payloadContextMarshaller.afterPropertiesSet();
	}

	private static GenericMessage<String> helloWorld() {
		return new GenericMessage<>("Hello, EMC!");
	}

	public static void main(String[] args) throws Exception {
		// System.setProperty("spring.profiles.active", "standalone,
		// activityMonitorCanSuppressPayload");
		logger.info("ttttt");
		System.setProperty("spring.profiles.active", "devWithJMS"); // local properties
		System.setProperty("application.configuration.name", "activity-monitor-test-client");
		System.setProperty("cic.process.id", "activity-monitor-test-process");
		createPcMarshaller();
		ConfigurableApplicationContext ctx = new ClassPathXmlApplicationContext(
				"classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml",
				"classpath:com/emc/it/eis/activity/test-flow-context.xml");
		MessageChannel inbound = ctx.getBean("inbound", MessageChannel.class);
		inbound.send(helloWorld());
		/*
		 * inbound.send(json()); inbound.send(errorMessage());
		 */
		/*
		 * MessageChannel warning = ctx.getBean("warning", MessageChannel.class);
		 * warning.send(helloWorldWithPayloadContext());
		 */
// 		MessageChannel chan = ctx.getBean("cic.activity.monitor.out", MessageChannel.class);
// 		chan.send(new GenericMessage<String>("junk"));

		ctx.close();
	}

}

package com.emc.it.eis.activity.domain;

public class ActvityKafka {
	private String status;
	/** values should PROCESS, LOG, SAVE_POINT */
	private String event;
	private String eventCode;
	/** Component or flow task that initiated the event */
	private String eventSubcode;
	private String step;
	private String summary;
	private String detail;
	private String business_identifier;
	private String alternate_business_identifier;
	private String global_transaction_id;
	private String transactionMode;
	private String domain;
	private String environment;
	private String service_name;
	private String date_time;
	private String guid;
	private String serviceVersion;
	private String app_name;
	private String appUser;
	private String host_name;
	private String thread_name;
	private String payload;	
	private String receiver;
	private String isPayloadTempered;

	public String getIsPayloadTempered() {
		return isPayloadTempered;
	}

	public void setIsPayloadTempered(String isPayloadTempered) {
		this.isPayloadTempered = isPayloadTempered;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	public String getDate_time() {
		return date_time;
	}

	public void setDate_time(String dateTime) {
		this.date_time = dateTime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getEvent() {
		return event;
	}

	public void setEvent(String event) {
		this.event = event;
	}

	public String getEventCode() {
		return eventCode;
	}

	public void setEventCode(String eventCode) {
		this.eventCode = eventCode;
	}

	public String getEventSubcode() {
		return eventSubcode;
	}

	public void setEventSubcode(String eventSubcode) {
		this.eventSubcode = eventSubcode;
	}

	public String getStep() {
		return step;
	}

	public void setStep(String step) {
		this.step = step;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public String getBusiness_identifier() {
		return business_identifier;
	}

	public void setBusiness_identifier(String businessIdentifier) {
		this.business_identifier = businessIdentifier;
	}

	public String getAlternate_business_identifier() {
		return alternate_business_identifier;
	}

	public void setAlternate_business_identifier(String alternateBusinessIdentifier) {
		this.alternate_business_identifier = alternateBusinessIdentifier;
	}

	public String getGlobal_transaction_id() {
		return global_transaction_id;
	}

	public void setGlobal_transaction_id(String globalTransactionId) {
		this.global_transaction_id = globalTransactionId;
	}

	public String getTransactionMode() {
		return transactionMode;
	}

	public void setTransactionMode(String transactionMode) {
		this.transactionMode = transactionMode;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getEnvironment() {
		return environment;
	}

	public void setEnvironment(String environment) {
		this.environment = environment;
	}

	public String getService_name() {
		return service_name;
	}

	public void setService_name(String serviceName) {
		this.service_name = serviceName;
	}

	public String getServiceVersion() {
		return serviceVersion;
	}

	public void setServiceVersion(String serviceVersion) {
		this.serviceVersion = serviceVersion;
	}

	public String getApp_name() {
		return app_name;
	}

	public void setApp_name(String appName) {
		this.app_name = appName;
	}

	public String getAppUser() {
		return appUser;
	}

	public void setAppUser(String appUser) {
		this.appUser = appUser;
	}

	public String getHost_name() {
		return host_name;
	}

	public void setHost_name(String hostName) {
		this.host_name = hostName;
	}

	public String getThread_name() {
		return thread_name;
	}

	public void setThread_name(String threadName) {
		this.thread_name = threadName;
	}

	public String getPayload() {
		return payload;
	}

	public void setPayload(String payload) {
		this.payload = payload;
	}

}

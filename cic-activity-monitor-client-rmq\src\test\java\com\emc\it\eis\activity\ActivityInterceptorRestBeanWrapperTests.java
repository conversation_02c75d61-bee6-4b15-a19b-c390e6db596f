package com.emc.it.eis.activity;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;

import com.emc.it.eis.activity.restclient.ActivityLoggingConstants;
import com.emc.it.eis.activity.restclient.ActmonEventBean;
import com.emc.it.enterprise.data.v1.CreateEventRequest;

@ContextConfiguration
class ActivityInterceptorRestBeanWrapperTests 
{

	private Logger logger = LoggerFactory.getLogger(getClass());

	@Autowired
	@Qualifier("activityBean")
	ActmonEventBean activityBean;

	@Autowired
	ActmonLoggerImpl baseActmonLoggerImpl;

	@BeforeEach
	void setup() {
		baseActmonLoggerImpl.setActivityBean(activityBean);
	}

	@Test
	@Disabled
	void beanLoading() {
		logger.info("Calling prepare event request...");
		CreateEventRequest cer = baseActmonLoggerImpl.prepareCreateEventRequest("START", "222", "aaa111-22-33-dd-ff-44",
				"111111-asssssssss-333333333-qq", "testBeanLoading", null, null, "summary", "C11", null);
		assertThat(cer, is(notNullValue()));
		String result = baseActmonLoggerImpl.objectToJson(cer);
		Map<String, Boolean> activityExpressions = activityBean.getActivityLoggingAttributes();
		assertTrue(activityExpressions.get(ActivityLoggingConstants.ACTMON_LOGGING_ENABLED));
		logger.info(result);

		/**
		 * assert that date coming back is represented as timestamp only we do not want
		 * all the date fields to be serialized
		 */
		assertThat(result, not(containsString("weekOfWeekyear")));
		assertThat(result, containsString("transactionDateTime"));
		assertThat(result, containsString("timeZoneCode"));
		assertThat(result, containsString("daylightSavingTimeIndicator"));
	}

}

package com.emc.it.eis.activity;

import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;
import org.springframework.jmx.export.annotation.ManagedOperationParameters;
import org.springframework.jmx.export.annotation.ManagedResource;

import com.emc.it.enterprise.data.v1.CreateEventRequest;

@ManagedResource(objectName = "modelmbean:type=actmon-logging-properties-annotated", description = "Actmon logging properties")
public class ActmonLoggingProperties {

	private Boolean actmonLoggingEnabled;
	private String actmonFlow;
	private Boolean actmonStartEnabled;
	private Boolean actmonTerminatedEnabled;
	private Boolean actmonFailedEnabled;
	private Boolean actmonInfoEnabled;
	private Boolean actmonWarningEnabled;
	private Boolean actmonCompleteEnabled;

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonLoggingEnabled() {
		return actmonLoggingEnabled;
	}

	@ManagedOperation(description = "setActmonLoggingEnabled")
	@ManagedOperationParameters({
	@ManagedOperationParameter(name = "actmonLoggingEnabled", description = "The  parameter to set the actmon logging variable to true or false")})
	public void setActmonLoggingEnabled(Boolean actmonLoggingEnabled) {
		this.actmonLoggingEnabled = actmonLoggingEnabled;
	}
	
	@ManagedOperation(description = "Get the setting of actmon flow varibale")
	public String getActmonFlow() {
		return actmonFlow;
	}

	@ManagedOperation(description = "setActmonFlow")
	@ManagedOperationParameters({
	@ManagedOperationParameter(name = "actmonFlow", description = "The  parameter to set the actmon flow variable to kafka or rabbit")})
	public void setActmonFlow(String actmonFlow) {
		this.actmonFlow = actmonFlow;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonStartEnabled() {
		return actmonStartEnabled;
	}

	@ManagedOperation(description = "setActmonStartEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonStartEnabled", description = "The  parameter to set the actmon start logging variable to true or false")})
	public void setActmonStartEnabled(Boolean actmonStartEnabled) {
		this.actmonStartEnabled = actmonStartEnabled;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonTerminatedEnabled() {
		return actmonTerminatedEnabled;
	}

	@ManagedOperation(description = "setActmonTerminatedEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonTerminatedEnabled", description = "The  parameter to set the actmon terminated logging variable to true or false")})
	public void setActmonTerminatedEnabled(Boolean actmonTerminatedEnabled) {
		this.actmonTerminatedEnabled = actmonTerminatedEnabled;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonFailedEnabled() {
		return actmonFailedEnabled;
	}

	@ManagedOperation(description = "setActmonFailedEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonFailedEnabled", description = "The  parameter to set the actmon failed logging variable to true or false")})
	public void setActmonFailedEnabled(Boolean actmonFailedEnabled) {
		this.actmonFailedEnabled = actmonFailedEnabled;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonInfoEnabled() {
		return actmonInfoEnabled;
	}

	@ManagedOperation(description = "setActmonInfoEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonInfoEnabled", description = "The  parameter to set the actmon info logging variable to true or false")})
	public void setActmonInfoEnabled(Boolean actmonInfoEnabled) {
		this.actmonInfoEnabled = actmonInfoEnabled;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonWarningEnabled() {
		return actmonWarningEnabled;
	}

	@ManagedOperation(description = "setActmonWarningEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonWarningEnabled", description = "The  parameter to set the actmon warn logging variable to true or false")})
	public void setActmonWarningEnabled(Boolean actmonWarningEnabled) {
		this.actmonWarningEnabled = actmonWarningEnabled;
	}

	@ManagedOperation(description = "Get the setting of actmon logging varibale")
	public Boolean getActmonCompleteEnabled() {
		return actmonCompleteEnabled;
	}

	@ManagedOperation(description = "setActmonCompleteEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "actmonCompleteEnabled", description = "The  parameter to set the actmon complete logging variable to true or false")})
	public void setActmonCompleteEnabled(Boolean actmonCompleteEnabled) {
		this.actmonCompleteEnabled = actmonCompleteEnabled;
	}

	public boolean checkActmonLogginEnabled(CreateEventRequest createEventRequest) {
		String status = createEventRequest.getDocument().getEventActivity().getStatus().getValue();
		return !((status.equals(Status.STARTED.name()) && !getActmonStartEnabled())
		|| (status.equals(Status.COMPLETED.name()) && !getActmonCompleteEnabled())
		|| (status.equals(Status.TERMINATED.name()) && !getActmonTerminatedEnabled())
		|| (status.equals(Status.FAILED.name()) && !getActmonFailedEnabled())
		|| (status.equals(Status.INFO.name()) && !getActmonInfoEnabled())
		|| (status.equals(Status.WARNING.name()) && !getActmonWarningEnabled()));
	}

}

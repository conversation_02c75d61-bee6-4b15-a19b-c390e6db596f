package com.emc.it.eis.activity.service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.apache.kafka.common.errors.NetworkException;
import org.apache.kafka.common.errors.RecordTooLargeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.async.DeferredResult;

import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.domain.ActvityKafka;
import com.emc.it.eis.activity.domain.Result;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AsyncHelper {

	@Value("${failover.enabled}")
	private String failOverKey;

	private ActivityService activityService;

	ApplicationContext context;
	
	@Autowired
	private ObjectMapper objectMapper;

	public ObjectMapper getObjectMapper() {
		return objectMapper;
	}

	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	public AsyncHelper(ActivityService activityService, ApplicationContext context) {
		this.activityService = activityService;
		this.context = context;
	}
	/**
	 * 
	 * @param activity
	 * @param deferredResult
	 * @throws Exception
	 */

	public void processKafkaActivity(Activity activity, DeferredResult<ResponseEntity<Result>> deferredResult,
			String topicName) throws Exception {

			ActvityKafka act = activityService.getKafkaActivity(activity, topicName);

			@SuppressWarnings("unchecked")
			Map<String, Object> activityMap = getObjectMapper().convertValue(act, Map.class);

			/*
			 * if (!StringUtils.isEmpty(activity.getNameValuePairs())) {
			 * log.info("Name Value pair is not null {}", activity.getNameValuePairs());
			 * activity.getNameValuePairs() .forEach(nameValue ->
			 * activityMap.put(nameValue.getName(), nameValue.getValue())); }
			 */

			if (!ObjectUtils.isEmpty(activity.getNameValuePairs())) {
				if (log.isInfoEnabled()) {

					log.info("Name Value pair is not null {}", activity.getNameValuePairs());
				}

				ObjectMapper objMapper = getObjectMapper();

				String nvpValues = objMapper.writeValueAsString(activity.getNameValuePairs());

				activityMap.put("nvp", nvpValues);

			}

			log.info("Fail Over is not enabled");
			CompletableFuture<SendResult<Long, Map<String, Object>>> future = activityService.sendToKafka(activityMap,
					topicName);

			future.whenComplete((sendResult, error) -> {

				if (ObjectUtils.isEmpty(error)) {
					if (log.isInfoEnabled()) {

						log.info(
								"message posted successfully for activity details - Business Identifier {}  and App Name as {}",
								activity.getBusinessIdentifier(), activity.getAppName());
					}

					if (!ObjectUtils.isEmpty(topicName)) {
						// topic name configured for e2e, hence no alerts required
						log.info("Specific Topic name  is {}", topicName);

					} else {
						log.info(
						"Success push to kafka for AIC needs to send activity message to Event Rules Q alert framework");

						// activityService.processPayload(activity, act);
						activityService.processAlert(activity);
					}
				} else
				{
					if (error.getCause() instanceof RecordTooLargeException) {
						log.info("Error Occurred while pushing to kafka with Record Too large Exception, hence logging an INFO Event with error details");

						try {
							activityMap.put("isPayloadTempered", "true");
							activityMap.put("payload", error.getCause().getMessage());
							activityService.sendToKafka(activityMap, topicName);

							if (log.isInfoEnabled()) {


								log.info("Messages posted succesfully with business id as{} with error payload",
										activity.getBusinessIdentifier());
							}

						} catch (Exception e) {
							log.error(
							"Error while sending to kafka with under instance of record too large exception {}",
							e.getCause());
						}
					} else if (error.getCause() instanceof NetworkException || error instanceof NetworkException) {

						log.info(
						"Error Occurred while pushing to kafka with Network Exception, hence resending the message again");

						activityService.flushTemplate(topicName);

						try {
							if (log.isInfoEnabled()) {

								log.info("Processing the records again after newtork exception encountered for {}",
										activity.getAppName());
							}
							processKafkaActivity(activity, deferredResult, topicName);
						} catch (Exception e) {
							log.error("Error Processing the records again after newtork exception encountered for {}",
							activity.getAppName());
							e.printStackTrace();
						}

					} else {
						if (log.isInfoEnabled()) {

							log.info("Exception while sending the message and exception is {}", error.getCause());
						}
						log.error(
						"Error while posting message for activity details - Business Identifier {}  and App Name as {}",
						activity.getBusinessIdentifier(), activity.getAppName());
					}

				}
			});
	}

	/**
	 * 
	 * @param activity
	 * @param deferredResult
	 * @throws Exception
	 */

	public void processKafkaActivityDefault(Activity activity, DeferredResult<ResponseEntity<Result>> deferredResult,
			String topicName) throws Exception {

		ActvityKafka act = activityService.getKafkaActivity(activity, topicName);

		@SuppressWarnings("unchecked")
		Map<String, Object> activityMap = getObjectMapper().convertValue(act, Map.class);

		/*
		 * if (!StringUtils.isEmpty(activity.getNameValuePairs())) {
		 * log.info("Name Value pair is not null {}", activity.getNameValuePairs());
		 * activity.getNameValuePairs() .forEach(nameValue ->
		 * activityMap.put(nameValue.getName(), nameValue.getValue())); }
		 */

		if (!ObjectUtils.isEmpty(activity.getNameValuePairs())) {
			if (log.isInfoEnabled()) {

				log.info("Name Value pair is not null {}", activity.getNameValuePairs());
			}

			ObjectMapper objMapper = getObjectMapper();

			String nvpValues = objMapper.writeValueAsString(activity.getNameValuePairs());

			activityMap.put("nvp", nvpValues);

		}
		CompletableFuture<SendResult<Long, Map<String, Object>>> future = activityService.sendToKafka(activityMap,
				topicName);

		future.whenComplete((sendResult, error) -> {

			if (ObjectUtils.isEmpty(error)) {
				if (log.isInfoEnabled()) {

					log.info(
							"message posted successfully for activity details - Business Identifier {}  and App Name as {}",
							activity.getBusinessIdentifier(), activity.getAppName());
				}

				if (!ObjectUtils.isEmpty(topicName)) {
					// topic name configured for e2e, hence no alerts required
					log.info("Specific Topic name  is {}", topicName);

				} else {
					log.info(
					"Success push to kafka for AIC needs to send activity message to Event Rules Q alert framework");

					// activityService.processPayload(activity, act);
					activityService.processAlert(activity);
				}
			} else
			{
				if (error.getCause() instanceof RecordTooLargeException) {
					log.info(
					"Error Occurred while pushing to kafka with Record Too large Exception, hence logging an INFO Event with error details");

					try {
						activityMap.put("isPayloadTempered", "true");
						activityMap.put("payload", error.getCause().getMessage());

						activityService.sendToKafka(activityMap, topicName);

						if (log.isInfoEnabled()) {


							log.info("Messages posted succesfully with business id as{} with error payload",
									activity.getBusinessIdentifier());
						}

					} catch (Exception e) {
						log.error("Error while sending to kafka with under instance of record too large exception {}",
						e.getCause());
					}
				} else if (error.getCause() instanceof NetworkException || error instanceof NetworkException) {

					log.info(
					"Error Occurred while pushing to kafka with Network Exception, hence resending the message again");

					activityService.flushTemplate(topicName);

					try {
						if (log.isInfoEnabled()) {

							log.info("Processing the records again after network exception encountered for {}",
									activity.getAppName());
						}
						processKafkaActivityDefault(activity, deferredResult, topicName);
					} catch (Exception e) {
						log.error("Error Processing the records again after network exception encountered for {}",
						activity.getAppName());
						e.printStackTrace();
					}

				} else {
					if (log.isInfoEnabled()) {

						log.info("Exception while sending the message and exception is {}", error.getCause());
					}
					log.error(
					"Error while posting message for activity details - Business Identifier {}  and App Name as {}",
					activity.getBusinessIdentifier(), activity.getAppName());

				}
			}
		});
	}
}

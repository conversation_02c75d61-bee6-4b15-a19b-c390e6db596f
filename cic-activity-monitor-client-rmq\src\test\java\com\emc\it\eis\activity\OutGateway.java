/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.activity;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @since 2.0.4
 *
 */
public class OutGateway implements ActivityOutGateway {

	private final BlockingQueue<String> queue = new LinkedBlockingQueue<>();
	private boolean failSend;
	private int failAfter;

	public void setFailSend(boolean failSend) {
		this.failSend = failSend;
	}

	public void setFailAfter(int failAfter) {
		this.failAfter = failAfter;
	}

	public BlockingQueue<String> getQueue() {
		return queue;
	}

	@Override
	public void sendActivity(String activity) {
		if (this.failSend && this.failAfter-- <= 0) {
			throw new RuntimeException("Planned failure");
		}
		queue.add(activity);
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int="http://www.springframework.org/schema/integration"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
	
	<context:property-placeholder location="classpath:cic-activity-monitor-client.properties"/>
	<import resource="activity-monitor-common-rs-context.xml"/>
	
	<int:channel id="cic.activity.monitor.out" >
		<int:interceptors>
			<bean parent="startInterceptor"/>
		</int:interceptors>
	</int:channel>
	<int:channel id="cic.activity.reply.back.channel">
		<int:queue />
	</int:channel>
	
	<int:bridge input-channel="cic.activity.monitor.out" output-channel="cic.activity.reply.back.channel"/>


	<bean id="httpServer1" class="com.emc.it.eis.common.http.HttpServerFactory">
		<constructor-arg name="port" value="8546" />
		<constructor-arg name="contextBindings">
			<map>
				<entry key="/cic-activity-monitor-webservice/actmon.ws/json" value-ref="cicActmonWSResponseHandler" />
			</map>
		</constructor-arg>
	</bean>
	
	<bean id="cicActmonWSResponseHandler" class="com.emc.it.eis.common.ws.MockSOAPResponseHandler">
		<constructor-arg value="classpath:data/cic-ws-response.xml" />
	</bean>

</beans>

package com.emc.it.eis.activity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import org.apache.commons.codec.binary.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaMapper;



public class ActmonClientUtil implements EnvironmentAware {
	private static final Logger logger = LoggerFactory.getLogger(ActmonClientUtil.class);
	private static Environment environment;
	private static final ObjectMapper objectMapper = new JodaMapper()
			.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true);	

	/**
	 * Get pod host information for Kubernetes deployment.
	 * Replaces Cloud Foundry getCFHost method for Kubernetes migration.
	 *
	 * Format: {namespace}_{podInstance}_{environment}
	 *
	 * @return String representing the pod host identifier
	 */
	public static String getPodHost() {
		String namespace = "";
		String appName = "";
		String podInstance = "";
		String k8sEnv = "dtc";

		String podHost = "";

		try {
			// Get Kubernetes namespace from environment variable (with system property fallback for testing)
			namespace = getEnvironmentValue("MY_POD_NAMESPACE");
			if (ObjectUtils.isEmpty(namespace)) {
				namespace = getEnvironmentValue("KUBERNETES_NAMESPACE");
			}
			if (ObjectUtils.isEmpty(namespace)) {
				namespace = "default";
			}

			// Get app name from app_config_name environment variable (defined in Helm chart)
			appName = getEnvironmentValue("app_config_name");
			if (ObjectUtils.isEmpty(appName)) {
				appName = "unknown-app";
			}

			// Get pod instance identifier from pod name
			String podName = getEnvironmentValue("MY_POD_NAME");
			if (ObjectUtils.isEmpty(podName)) {
				podName = getEnvironmentValue("HOSTNAME");
			}

			if (!ObjectUtils.isEmpty(podName)) {
				// Extract instance identifier from pod name (e.g., "app-deployment-12345-abcde" -> "12345-abcde")
				String[] parts = podName.split("-");
				if (parts.length >= 2) {
					// Take the last 2 parts as instance identifier
					podInstance = parts[parts.length - 2] + "-" + parts[parts.length - 1];
				} else {
					podInstance = podName;
				}
			} else {
				podInstance = "0";
			}

			podHost = namespace + "_" + podInstance + "_" + k8sEnv;

		} catch (Exception e) {
			logger.error("Error getting pod host information", e);
			podHost = "unknown_0_" + k8sEnv;
		}

		logger.info("ActmonClientUtil::getPodHost returning podHost={}", podHost);
		return podHost;
	}

	/**
	 * Helper method to get environment variable value with system property fallback for testing.
	 * This allows tests to use system properties instead of environment variables.
	 *
	 * @param key the environment variable name
	 * @return the value from environment variable or system property, or null if not found
	 */
	private static String getEnvironmentValue(String key) {
		String value = System.getenv(key);
		if (value == null) {
			// Fallback to system property for testing
			value = System.getProperty(key);
		}
		return value;
	}

	/**
	 * @deprecated Use getPodHost() instead. This method is kept for backward compatibility
	 * during the migration from Cloud Foundry to Kubernetes.
	 */
	@Deprecated
	public static String getCFHost() {
		logger.warn("getCFHost() is deprecated. Use getPodHost() instead for Kubernetes deployment.");
		return getPodHost();
	}

	public static String getActiveProfile() {
		String currentProfile = "default";
		if (null != environment) {
			if (environment.getActiveProfiles().length > 0) {
				currentProfile = environment.getActiveProfiles()[0];
			} else {
				currentProfile = environment.getDefaultProfiles()[0];
			}
		}
		return currentProfile;
	}

	@Override
	public void setEnvironment(Environment environment) {
		ActmonClientUtil.environment = environment;
	}

	public static long getSize(String str) {
		long size = 0;
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(str)) {
			size = StringUtils.getBytesUtf8(str).length;
		}
		return size;
	}

	public static void main(String[] args) {
		int threemb = 3 * 1024 * 1024;
		if (getSize("payload size") > threemb) {
			logger.info("size > 3mb");
		} else {
			logger.info("size < 3mb");
		}
	}
	
	public static DateTime processDefaultDate() throws ParseException {

		Date date = new Date();

		SimpleDateFormat format = getDateTimeFormat();

		String formattedDate = format.format(date);

		return new DateTime(format.parse(formattedDate));
	}
	
	/**
	 * Get Date time based on UTC
	 * 
	 * @return
	 */
	private static SimpleDateFormat getDateTimeFormat() {
		// date_time
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
		format.setTimeZone(TimeZone.getTimeZone("UTC"));
		return format;
	}
}

package com.emc.it.eis.activity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import org.apache.commons.codec.binary.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaMapper;

import io.pivotal.cfenv.core.CfApplication;

public class ActmonClientUtil implements EnvironmentAware {
	private static final Logger logger = LoggerFactory.getLogger(ActmonClientUtil.class);
	private static Environment environment;
	private static final ObjectMapper objectMapper = new JodaMapper()
			.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true);	

	/**
	 * If the application run within PCF then provide details of PCF in actmon
	 * column hostname
	 * 
	 * @param servicesString
	 * @return
	 */
	public static String getCFHost() {
		Map<String, Object> rawServices;
		String appName = "";
		String spaceName = "";
		Integer appIndex = null;
		String pcfEnv = "dtc";
		
		String cfHost = "";
		
		if (!ObjectUtils.isEmpty(cfApplication)) {			
			spaceName = cfApplication.getSpaceName();		
			appName= cfApplication.getApplicationName();		
			appIndex = cfApplication.getInstanceIndex();
			cfHost = spaceName + "_" + appIndex + "_" + pcfEnv;
		}
		logger.info("ActmonClientUtil::getCFHost returning cfHost={}", cfHost);
		return cfHost;
	}

	public static String getActiveProfile() {
		String currentProfile = "default";
		if (null != environment) {
			if (environment.getActiveProfiles().length > 0) {
				currentProfile = environment.getActiveProfiles()[0];
			} else {
				currentProfile = environment.getDefaultProfiles()[0];
			}
		}
		return currentProfile;
	}

	@Override
	public void setEnvironment(Environment environment) {
		ActmonClientUtil.environment = environment;
	}

	public static long getSize(String str) {
		long size = 0;
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(str)) {
			size = StringUtils.getBytesUtf8(str).length;
		}
		return size;
	}

	public static void main(String[] args) {
		int threemb = 3 * 1024 * 1024;
		if (getSize("payload size") > threemb) {
			logger.info("size > 3mb");
		} else {
			logger.info("size < 3mb");
		}
	}
	
	public static DateTime processDefaultDate() throws ParseException {

		Date date = new Date();

		SimpleDateFormat format = getDateTimeFormat();

		String formattedDate = format.format(date);

		return new DateTime(format.parse(formattedDate));
	}
	
	/**
	 * Get Date time based on UTC
	 * 
	 * @return
	 */
	private static SimpleDateFormat getDateTimeFormat() {
		// date_time
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
		format.setTimeZone(TimeZone.getTimeZone("UTC"));
		return format;
	}
}

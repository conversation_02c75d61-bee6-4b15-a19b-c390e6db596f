#
#cic activity monitor client configuration
#
#used in dev profile
#
#Property Sheet cic-activity-monitor
#
#Common properties required for activity monitor 
#
cic.activity.monitor.amqp.fanout.exchange.name=AIC.ACTMON.EVENT.DIR.EXG
cic.activity.monitor.amqp.fanout.queue.routing.key=AIC.ACTMON.EVENT.Q
#
#
#Common Rabbit MQ Properties
#
btm.amqp.rmq.username=guest	
btm.amqp.rmq.virtual.host=/	
btm.amqp.rmq.host=msgdevcls03.isus.emc.com	
btm.amqp.rmq.password=guest	
btm.amqp.rmq.port=5672


cic.actmon.host=localhost
cic.actmon.port=8080
cic.actmon.context.path=cic-activity-monitor-webservice/actmon.ws
cic.actmon.ws.available=true
cic.actmon.port1=8099

# Credentials
cic.rest.webservice.username=aicrestuser
cic.rest.webservice.password=aicrestuser


activity.monitor.ws.so.timeout=30000



kafka.flow.enabled=false
#byte size of 3 MB (3*1024*1024)
kafka.payload.max.size.bytes=3145728
actmon.custom.security.user.name=aicrestuser
actmon.custom.security.user.password=aicrestuser
app.topic.actmon=ACTMON.SHARED.DEV.P4.R2

actmon.default.flow=kafka
actmon.kafka.broker.port=9093

scheduler.inmemory.fixeddelay=300000
scheduler.inmemory.initialdelay=300000

scheduler.inmemory.failover.fixeddelay=120000
scheduler.inmemory.failover.initialdelay=120000

#spring.kafka.bootstrap-servers=ausilkaf501.us.dell.com:9092,ausilkaf502.us.dell.com:9092,ausilkaf503.us.dell.com:9092,ausilkaf504.us.dell.com:9092
#spring.kafka.bootstrap-servers=localhost:9092
#spring.kafka.bootstrap-servers=ausilkaf501.us.dell.com:9092,ausilkaf502.us.dell.com:9092,ausilkaf503.us.dell.com:9092,ausilkaf504.us.dell.com:9092
#spring.kafka.failover-bootstrap-servers=ausilkaf501.us.dell.com:9092,ausilkaf502.us.dell.com:9092,ausilkaf503.us.dell.com:9092,ausilkaf504.us.dell.com:9092
spring.kafka.bootstrap-servers=ausilkaf501.us.dell.com:9093,ausilkaf502.us.dell.com:9093,ausilkaf503.us.dell.com:9093,ausilkaf504.us.dell.com:9093
spring.kafka.failover-bootstrap-servers=ausilkaf501.us.dell.com:9093,ausilkaf502.us.dell.com:9093,ausilkaf503.us.dell.com:9093,ausilkaf504.us.dell.com:9093

spring.kafka.ssl.protocol=TLSv1.2
spring.kafka.properties.security.protocol=SASL_SSL
spring.kafka.properties.sasl.mechanism=GSSAPI
spring.kafka.properties.sasl.kerberos.service.name=kafka
spring.kafka.ssl.trust-store-type=JKS
spring.kafka.ssl.truststore-location=/home/<USER>/app/BOOT-INF/classes/tls/kafka.client.truststore.jks
spring.kafka.ssl.trust-store-password=good8008

failover.enabled=isFailOverEnabled


actmon.exchange.event.dir.name=AIC.BTM.EVNT.KAFKA.DIR.EXG

actmon.queue.event.rule.name=AIC.BTM.EVNT.RULES.Q

actmon.queue.alert.routing.key=AIC.BTM.KAFKA.ALERT

spring.kafka.producer.acks=all
logging.level.org.springframework.web=INFO
logging.level.com.dell.it.eis=DEBUG
spring.rabbitmq.host=intmsgdev06.isus.emc.com
spring.rabbitmq.port=5672
spring.rabbitmq.username=AICUserIntDev
spring.rabbitmq.password=aicuserintdev
spring.rabbitmq.virtualHost=ACTMON
actmon.exchange.name=AIC.BTM.EVNT.FO.EXG
actmon.queue.name=AIC.BTM.EVNT.Q
payload.exchange.name=AIC.ACTMON.LP
payload.queue.name=AIC.ACTMON.LP.QUEUE
api.security.username=aicrestuser
api.security.password=aicrestuser
active.monitoring.service.messagingsystem=rabbit
logging.level.root=INFO
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=180000
redis.service.url=https://redis-service.cfd.isus.emc.com


# Executor Configurations

actmon.service.core.pool.size=10
actmon.service.max.pool.size=20
actmon.service.queue.capacity=50

actmon.scheduler.cron=0 0/30 * * * ?

inventory.service.url=https://inventory-service-eis.cfcp.isus.emc.com/ 
inventory.custom.security.user.name=eisrestuser
inventory.custom.security.user.password=eisrestuser




feign.retry.max.attempt=3
feign.retry.backoff=8000


max.block.ms=360000
reconnect.backoff.ms=500
max.request.size.bytes=75000000

linger.ms.config=50
batch.size.config=400000

retry.count=5

reconnect.backoff.max.ms=5000
retry.backoff.ms=500

buffer.memory.config=1048576000
max.in.flight.req.per.connection=1
request.time.out.ms.config=180000



#Checking if kafka is up and down parameters
connections.max.idle.ms=50000
request.timeout.ms=70000
admin.client.timeout.ms=300000


spring.data.elasticsearch.repositories.enabled=true
# This property is deprecated: The transport client support is deprecated. Use other supported clients instead.
# 
# This property is deprecated: The transport client support is deprecated. Use other supported clients instead.
# spring.data.elasticsearch.cluster-nodes=ipselsdev01.isus.emc.com:9300
# This property is deprecated: The transport client support is deprecated. Use other supported clients instead.
# 
# This property is deprecated: The transport client support is deprecated. Use other supported clients instead.
# spring.data.elasticsearch.cluster-name=support-portal



#
#cic activity monitor client configuration
#
#used in dev profile
#
#Property Sheet cic-activity-monitor
#
#Common properties required for activity monitor 
#
cic.activity.monitor.amqp.fanout.exchange.name=AIC.ACTMON.EVENT.DIR.EXG
cic.activity.monitor.amqp.fanout.queue.routing.key=AIC.ACTMON.EVENT.Q
#
#
#Common Rabbit MQ Properties
#
btm.amqp.rmq.username=guest	
btm.amqp.rmq.virtual.host=/	
btm.amqp.rmq.host=msgdevcls03.isus.emc.com	
btm.amqp.rmq.password=guest	
btm.amqp.rmq.port=5672


cic.actmon.host=localhost
cic.actmon.port=8080
cic.actmon.context.path=cic-activity-monitor-webservice/actmon.ws
cic.actmon.ws.available=true
cic.actmon.port1=8099

# Credentials
cic.rest.webservice.username=aicrestuser
cic.rest.webservice.password=aicrestuser


activity.monitor.ws.so.timeout=30000



actmon.user.token=hrMBbk-SDcJR4RxbmGYJ
defined.truststore.location=/home/<USER>/app/WEB-INF/classes/tls/actmon/kafka/kafka.client.truststore.jks
actmon.gitlab.truststore.loc=https://localhost:12345/api/v4/projects/69198/repository/files/kafka.client.truststore.jks/raw?ref=master

spring.kafka.properties.ldap.sasl.mechanism=PLAIN
ldap.sasl.jaas.config= org.apache.kafka.common.security.plain.PlainLoginModule required username="svc_npaiccoredvkfka";
ldap.secret=secret

spring.security.oauth2.client.registration.dias-creds.provider=dias
spring.security.oauth2.client.registration.dias-creds.client-id=4acab72b-12fe-480a-aafd-6c3e3b852d2c
spring.security.oauth2.client.registration.dias-creds.client-secret=kBQVwb13
spring.security.oauth2.client.registration.dias-creds.authorization-grant-type=client_credentials


spring.security.oauth2.client.provider.dias.token-uri=https://oauth2-api-dev-dci.ausvdc02.pcf.dell.com/oauth2/api/v3/token


aic.oauth2.enabled.feignclient=true
aic.oauth2.enabled.resttemplate=true
aic.oauth2.enabled.webclient=false
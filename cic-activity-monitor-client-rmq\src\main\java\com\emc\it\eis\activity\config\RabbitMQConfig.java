package com.emc.it.eis.activity.config;

import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitMQConfig {

	@Value("${actmon.exchange.name}")
	private String actmonExchangeName;

	@Value("${actmon.queue.name}")
	private String actmonQueueName;

	@Value("${payload.exchange.name}")
	private String payloadExchangeName;

	@Value("${payload.queue.name}")
	private String payloadQueueName;

	@Value("${actmon.exchange.event.dir.name}")
	private String actmonExchangeEventName;

	@Value("${actmon.queue.event.rule.name}")
	private String actmonQueueEventName;

	@Value("${actmon.queue.alert.routing.key}")
	private String actmonQueueEventRoutingKey;

	@Value("${spring.kafka.bootstrap-servers}")
	private String bootstrapServers;

	@Value("${connections.max.idle.ms}")
	private String connMaxIdleMs;

	@Value("${request.timeout.ms}")
	private String reTimeOutMs;

	@Value("${admin.client.timeout.ms}")
	private int adminClientTimedoutMs;

	@Bean
	RabbitTemplate actmonRabbitEventTemplate(
			@Qualifier("commonBtmAmqpConnectionFactory") ConnectionFactory connectionFactory) {
		RabbitTemplate actmonRabbitTemplate = new RabbitTemplate(connectionFactory);
		actmonRabbitTemplate.setExchange(actmonExchangeEventName);
		actmonRabbitTemplate.setRoutingKey(actmonQueueEventRoutingKey);
		return actmonRabbitTemplate;
	}

	@Bean
	RabbitTemplate actmonRabbitTemplate(
			@Qualifier("commonBtmAmqpConnectionFactory") ConnectionFactory connectionFactory) {
		RabbitTemplate actmonRabbitTemplate = new RabbitTemplate(connectionFactory);
		actmonRabbitTemplate.setExchange(actmonExchangeName);
		actmonRabbitTemplate.setDefaultReceiveQueue(actmonQueueName);
		actmonRabbitTemplate.setRoutingKey(actmonQueueName);
		return actmonRabbitTemplate;
	}

	@Bean
	RabbitTemplate payloadRabbitTemplate(
			@Qualifier("commonBtmAmqpConnectionFactory") ConnectionFactory connectionFactory) {
		RabbitTemplate payloadRabbitTemplate = new RabbitTemplate(connectionFactory);
		payloadRabbitTemplate.setExchange(payloadExchangeName);
		payloadRabbitTemplate.setDefaultReceiveQueue(payloadQueueName);
		payloadRabbitTemplate.setRoutingKey(payloadQueueName);
		return payloadRabbitTemplate;
	}
	

}

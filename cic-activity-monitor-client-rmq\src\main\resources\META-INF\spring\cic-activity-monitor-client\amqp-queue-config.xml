<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int="http://www.springframework.org/schema/integration"
	xmlns:int-amqp="http://www.springframework.org/schema/integration/amqp"
	xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
	xsi:schemaLocation="http://www.springframework.org/schema/integration/amqp http://www.springframework.org/schema/integration/amqp/spring-integration-amqp.xsd
		http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<import resource="classpath:/META-INF/spring/cic-config/business-transaction-monitor-amqp-config.xml" />

	<int-amqp:outbound-channel-adapter id="cic.activity.monitor.outbound.adapter"
		channel="cic.activity.monitor.out" amqp-template="btmRabbitTemplateWithSearlizeMC"
		exchange-name="${cic.activity.monitor.amqp.event.exg}" routing-key="${cic.activity.monitor.amqp.event.q.routing.key}"
		mapped-request-headers="${cic.activity.monitor.amqp.event.q.headers.out:EMC.*}">
		<int-amqp:request-handler-advice-chain>
			<ref bean="commonRetryAdviceBean" />
		</int-amqp:request-handler-advice-chain>
	</int-amqp:outbound-channel-adapter>

</beans>
package com.emc.it.eis.activity;

import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;
import org.springframework.jmx.export.annotation.ManagedOperationParameters;
import org.springframework.jmx.export.annotation.ManagedResource;

import com.emc.it.eis.activity.domain.Activity;


@ManagedResource(objectName = "modelmbean:type=rules-configuration-properties-annotated", description = "Rules configuration properties")
public class RulesConfigurationProperties {

	private Boolean rulesConfigurationEnabled;
	private Boolean rulesStartEnabled;
	private Boolean rulesTerminatedEnabled;
	private Boolean rulesFailedEnabled;
	private Boolean rulesInfoEnabled;
	private Boolean rulesWarningEnabled;
	private Boolean rulesCompleteEnabled;

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesConfigurationEnabled() {
		return rulesConfigurationEnabled;
	}

	@ManagedOperation(description = "setRulesConfigurationEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesConfigurationEnabled", description = "The  parameter to set the rules logging variable to true or false")})
	public void setRulesConfigurationEnabled(Boolean rulesConfigurationEnabled) {
		this.rulesConfigurationEnabled = rulesConfigurationEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesStartEnabled() {
		return rulesStartEnabled;
	}

	@ManagedOperation(description = "setRulesStartEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesStartEnabled", description = "The  parameter to set the rules start logging variable to true or false")})
	public void setRulesStartEnabled(Boolean rulesStartEnabled) {
		this.rulesStartEnabled = rulesStartEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesTerminatedEnabled() {
		return rulesTerminatedEnabled;
	}

	@ManagedOperation(description = "setRulesTerminatedEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesTerminatedEnabled", description = "The  parameter to set the rules terminated logging variable to true or false")})
	public void setRulesTerminatedEnabled(Boolean rulesTerminatedEnabled) {
		this.rulesTerminatedEnabled = rulesTerminatedEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesFailedEnabled() {
		return rulesFailedEnabled;
	}

	@ManagedOperation(description = "setRulesFailedEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesFailedEnabled", description = "The  parameter to set the rules failed logging variable to true or false")})
	public void setRulesFailedEnabled(Boolean rulesFailedEnabled) {
		this.rulesFailedEnabled = rulesFailedEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesInfoEnabled() {
		return rulesInfoEnabled;
	}

	@ManagedOperation(description = "setRulesInfoEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesInfoEnabled", description = "The  parameter to set the rules info logging variable to true or false")})
	public void setRulesInfoEnabled(Boolean rulesInfoEnabled) {
		this.rulesInfoEnabled = rulesInfoEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesWarningEnabled() {
		return rulesWarningEnabled;
	}

	@ManagedOperation(description = "setRulesWarningEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesWarningEnabled", description = "The  parameter to set the rules warn logging variable to true or false")})
	public void setRulesWarningEnabled(Boolean rulesWarningEnabled) {
		this.rulesWarningEnabled = rulesWarningEnabled;
	}

	@ManagedOperation(description = "Get the setting of rules logging varibale")
	public Boolean getRulesCompleteEnabled() {
		return rulesCompleteEnabled;
	}

	@ManagedOperation(description = "setRulesCompleteEnabled")
	@ManagedOperationParameters({
			@ManagedOperationParameter(name = "rulesCompleteEnabled", description = "The  parameter to set the rules complete logging variable to true or false")})
	public void setRulesCompleteEnabled(Boolean rulesCompleteEnabled) {
		this.rulesCompleteEnabled = rulesCompleteEnabled;
	}

	public boolean checkRulesConfigurationEnabled(Activity activity) {
		String status = activity.getStatus().toUpperCase();
		return !((status.equals(Status.STARTED.name()) && !getRulesStartEnabled())
		|| (status.equals(Status.COMPLETED.name()) && !getRulesCompleteEnabled())
		|| (status.equals(Status.TERMINATED.name()) && !getRulesTerminatedEnabled())
		|| (status.equals(Status.FAILED.name()) && !getRulesFailedEnabled())
		|| (status.equals(Status.INFO.name()) && !getRulesInfoEnabled())
		|| (status.equals(Status.WARNING.name()) && !getRulesWarningEnabled()));
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
	
    <util:map id="activityExpressions">
        <entry key="PayloadContext.MessageProfile.Process" value="'${cic.process.id:UNDEFINED}'"/>
        <entry key="PayloadContext.ApplicationProfile.AppName" value="'${application.configuration.name:UNDEFINED}'"/>
        <entry key="PayloadContext.TransactionProfile.GlobalTransactionID"
               value="headers['#{ T(com.emc.it.eis.common.integration.CICHeaders).GLOBAL_TRANSACTION_ID}']"/>
        <entry key="PayloadContext.TransactionProfile.Environment" value="'${am.environment:UNDEFINED}'"/>
        <entry key="PayloadContext.MessageProfile.ServiceName" value="'${am.serviceName:UNDEFINED}'"/>
        <entry key="PayloadContext.MessageProfile.ServiceVersion" value="'${am.serviceVersion:UNDEFINED}'"/>
        <entry key="PayloadContext.ApplicationProfile.AppUser" value="'${am.appUser:UNDEFINED}'"/>
    </util:map>

    <util:map id="headerExpressions">
        <entry key="PayloadContext.MessageProfile.Process" value="'${cic.process.id:UNDEFINED}'"/>
        <entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}"
               value="'${application.configuration.name:UNDEFINED}'"/>
        <entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}"
               value="'${cic.process.id:UNDEFINED}'"/>           
    </util:map>

    <!-- Bean for cic Activity monitor component -->
    <bean id="payloadActivityMonitorInterceptor" parent="basePayloadActivityMonitorInterceptor" abstract="true">
        <property name="baseHeaderExpressions" ref="headerExpressions"/>
        <property name="baseActivityExpressions" ref="activityExpressions"/>
    </bean>

    <bean id="activityMonitorInterceptor" parent="baseActivityMonitorInterceptor" abstract="true">
        <property name="baseHeaderExpressions" ref="headerExpressions"/>
        <property name="baseActivityExpressions" ref="activityExpressions"/>
    </bean>

    <bean id="actmonClientUtil" class="com.emc.it.eis.activity.ActmonClientUtil"/>
    
    <bean id="objectMapper" class="com.emc.it.eis.activity.mapper.CustomMapper" />

</beans>

package com.emc.it.eis.activity.test;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.gateway.RequestReplyExchanger;
import org.springframework.integration.handler.AbstractReplyProducingMessageHandler;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.SubscribableChannel;

public class ResendMessageOnExceptionHandler extends AbstractReplyProducingMessageHandler {
	private static final Logger logger = LoggerFactory.getLogger(ResendMessageOnExceptionHandler.class);

	private final SubscribableChannel errorChannel;

	private final RequestReplyExchanger gateway;

	private final AtomicInteger deliveryCount = new AtomicInteger(1);

	ResendMessageOnExceptionHandler(RequestReplyExchanger gateway, SubscribableChannel errorChannel) {
		this.errorChannel = errorChannel;
		this.gateway = gateway;
	}

	public void setDeliveryCount(int count) {
		deliveryCount.set(count);
	}

	@Override
	protected Object handleRequestMessage(Message<?> requestMessage) {
		Message<?> reply = null;
		boolean sending = true;
		while (sending) {
			try {
				reply = this.gateway.exchange(requestMessage);
				sending = false;
			} catch (Exception e) {
				sending = true;
				Map<String, Integer> headers = Map.of("JMSXDeliveryCount", deliveryCount.incrementAndGet());
				requestMessage = MessageBuilder.fromMessage(requestMessage).copyHeaders(headers).build();
			}
		}
		return reply;
	}

}

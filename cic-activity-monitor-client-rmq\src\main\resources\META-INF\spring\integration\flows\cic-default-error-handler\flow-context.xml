<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int-flow="http://www.springframework.org/schema/integration/flow" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:int="http://www.springframework.org/schema/integration"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:property-placeholder />

	<int-flow:flow-configuration>
		<int-flow:port-mapping>
			<int-flow:input-port name="input" channel="input-channel" />
		</int-flow:port-mapping>
	</int-flow:flow-configuration>

	<int-flow:flow id="common-error-handler">
		<props>
			<prop key="deliveryCountHeaderName">${deliveryCountHeaderName:#{T(com.emc.it.eis.common.integration.CICHeaders).DELIVERY_COUNT}}</prop>
			<prop key="decayFactor">${decayFactor:2.0}</prop>
			<prop key="delayTime">${delayTime:1000}</prop>
			<prop key="maximumRetries">${maximumRetries:4}</prop>
		</props>
	</int-flow:flow>

	<int-flow:flow id="activity-monitor-process-failed-event" />

	<int-flow:outbound-gateway flow="common-error-handler" input-channel="input-channel" output-channel="activity-monitor-channel" />

	<int-flow:outbound-gateway flow="activity-monitor-process-failed-event" input-channel="activity-monitor-channel" />

	<int:channel id="input-channel" />
	<int:channel id="activity-monitor-channel" />

</beans>

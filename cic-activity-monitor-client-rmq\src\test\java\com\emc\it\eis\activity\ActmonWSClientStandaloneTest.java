package com.emc.it.eis.activity;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.amqp.RabbitHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.activity.restclient.ActmonLogger;
import com.emc.it.eis.activity.restclient.ActmonLoggerImpl;

@SpringJUnitConfig
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"})
@EnableAutoConfiguration(exclude = {RabbitHealthContributorAutoConfiguration.class, DataSourceAutoConfiguration.class})
@ActiveProfiles("test")
public class ActmonWSClientStandaloneTest
{

	private static Logger logger = LoggerFactory.getLogger(ActmonWSClientStandaloneTest.class);
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER")	
	OAuth2AuthorizedClientManager authorizedClientManager;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_OAUTH2")
	RestTemplate restTemplate;

	public ActmonWSClientStandaloneTest() {
	}

	static {
		System.setProperty("spring.profiles.active", "standalone");
	}

	@Test
	void client() {
		logger.info("Starting..........");

		Class1 class1 = new Class1();
		class1.class1Method();
		logger.info("Ending...............");
	}

	static class Class1 {

		ActmonLogger actmonLogger = ActmonLoggerImpl.getInstance(getClass());

		public void class1Method() {
			Map<String, Object> map = new HashMap<>();
			map.put("key one", new HashMap<String, Integer>().put("count", 2));
			map.put("key2", "value 2");
			actmonLogger.logFailedEvent("test-bid-111", "212123", null, "", new Object(),
					new NullPointerException("This is a test of NPE on actmon"), null, map);
		}
	}
}

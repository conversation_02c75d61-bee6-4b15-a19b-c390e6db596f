cic.actmon.host=localhost
cic.actmon.port=8080
cic.actmon.context.path=cic-activity-monitor-webservice/actmon.ws
cic.actmon.ws.available=true
cic.actmon.port1=9090

#(Optional ERP is default value)
am.application.domain=ERP 
cic.process.id=projlist-sap
am.serviceNam=projlist-batch-sap
am.serviceVersion=2-SNAPSHOT

#(optional default is blank '')
am.transactionMode=

# Credentials
cic.rest.webservice.username=aicrestuser
cic.rest.webservice.password=aicrestuser

activity.monitor.ws.so.timeout=30000


spring.security.oauth2.client.registration.dias-creds.provider=dias
spring.security.oauth2.client.registration.dias-creds.client-id=4acab72b-12fe-480a-aafd-6c3e3b852d2c
spring.security.oauth2.client.registration.dias-creds.client-secret=kBQVwb13
spring.security.oauth2.client.registration.dias-creds.authorization-grant-type=client_credentials


spring.security.oauth2.client.provider.dias.token-uri=https://oauth2-api-dev-dci.ausvdc02.pcf.dell.com/oauth2/api/v3/token


aic.oauth2.enabled.feignclient=true
aic.oauth2.enabled.resttemplate=true
aic.oauth2.enabled.webclient=false

cic.activity.monitor.amqp.event.exg=AIC.ACTMON.EVENT.DIR.EXG

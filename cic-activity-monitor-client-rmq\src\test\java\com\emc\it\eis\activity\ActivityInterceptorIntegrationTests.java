/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.transform.Result;

import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.metrics.amqp.RabbitMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.xml.transform.StringResult;
import org.springframework.xml.transform.StringSource;

import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
//import com.emc.it.eis.contivo.integration.transformer.TransformerContext;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.DateTimeType;
import com.emc.it.enterprise.msg.v1.ObjectFactory;
import com.emc.it.enterprise.msg.v1.PayloadContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.joda.JodaModule;

import jakarta.xml.bind.Marshaller;

/**
 * <AUTHOR> Russell
 *
 */
@SpringJUnitConfig
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"})
@EnableAutoConfiguration(exclude = {RabbitMetricsAutoConfiguration.class})
public class ActivityInterceptorIntegrationTests  {


	private static final Logger logger = LoggerFactory.getLogger(ActivityInterceptorIntegrationTests.class);


	@Autowired
	MessageChannel inbound;

	@Autowired
	MessageChannel warning;

	@Autowired
	OutGateway outGateway;
	
	@Autowired
	ActivityService activityService;

	@Autowired
	AsyncHelper asyncHelper;

	@Autowired
	@Qualifier("eventMarshaller")
	Jaxb2Marshaller marshaller;

	private static Jaxb2Marshaller payloadContextMarshaller;

	private static final String profile = "dev"; // main() uses standalone

	static {
		System.setProperty("spring.profiles.active", profile);
		System.setProperty("application.configuration.name", "activity-monitor-test-client");
		System.setProperty("cic.process.id", "activity-monitor-test-process");
	}

	@BeforeEach
	void setup() throws Exception {
		createPcMarshaller();
	}

	static class TransformerContext {
		private Map<String, Object> data = new HashMap<>();

		public Map<String, Object> getData() {
			return data;
		}

		public void setData(Map<String, Object> data) {
			this.data = data;
		}
	}

	/**
	 * @throws Exception
	 */
	private static void createPcMarshaller() throws Exception {
		payloadContextMarshaller = new Jaxb2Marshaller();
		payloadContextMarshaller.setContextPath("com.emc.it.enterprise.msg.v1");
		Map<String, Object> props = new HashMap<>();
		props.put(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
		payloadContextMarshaller.setMarshallerProperties(props);
		payloadContextMarshaller.afterPropertiesSet();
	}

	@Test
	void simple() throws Exception {
		GenericMessage<String> hello = helloWorld();
		inbound.send(hello);
		Thread.sleep(2000);
		String out = outGateway.getQueue().poll();
		assertNotNull(out);
		logger.info(out);

		CreateEventRequest cer = (CreateEventRequest) marshaller.unmarshal(new StringSource(out));

		//assertEquals("Hello, world!", cer.getDocument().getEventActivity().getPayload());
		assertEquals("STARTED", cer.getDocument().getEventActivity().getStatus().getValue());
		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertEquals("activity-monitor-test-client", cer.getPayloadContext().getApplicationProfile().getAppName());
		assertEquals("activity-monitor-test-process", cer.getPayloadContext().getMessageProfile().getProcess());
		assertEquals(hello.getHeaders().getId().toString(), cer.getPayloadContext().getTransactionProfile().getGlobalTransactionID());
	}

	@Test
	void testJson() throws Exception {
		GenericMessage<List<?>> json = json();
		inbound.send(json);
		Thread.sleep(2000);
		String out = outGateway.getQueue().poll();
		assertNotNull(out);
		logger.info(out);

		CreateEventRequest cer = (CreateEventRequest) marshaller.unmarshal(new StringSource(out));

		StringWriter writer = new StringWriter();
		new ObjectMapper().registerModule(new JodaModule()).writeValue(writer, json.getPayload());
		String payload = writer.toString();

		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
		assertEquals("STARTED", cer.getDocument().getEventActivity().getStatus().getValue());
		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertEquals("activity-monitor-test-client", cer.getPayloadContext().getApplicationProfile().getAppName());
		assertEquals("activity-monitor-test-process", cer.getPayloadContext().getMessageProfile().getProcess());
		assertEquals(json.getHeaders().getId().toString(),
				cer.getPayloadContext().getTransactionProfile().getGlobalTransactionID());

	}

	@Test
	void error() throws Exception {
		ErrorMessage error = errorMessage();
		inbound.send(error);
		Thread.sleep(2000);
		String out = outGateway.getQueue().poll();
		assertNotNull(out);
		logger.info(out);

		CreateEventRequest cer = (CreateEventRequest) marshaller.unmarshal(new StringSource(out));

		Message<?> failedMessage = ((MessagingException) error.getPayload()).getFailedMessage();
		//assertEquals(failedMessage.getPayload(), cer.getDocument().getEventActivity().getPayload());
		assertEquals("STARTED", cer.getDocument().getEventActivity().getStatus().getValue());
		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertEquals("activity-monitor-test-client", cer.getPayloadContext().getApplicationProfile().getAppName());
		assertEquals("activity-monitor-test-process", cer.getPayloadContext().getMessageProfile().getProcess());
		assertEquals(failedMessage.getHeaders().getId().toString(), cer.getPayloadContext().getTransactionProfile().getGlobalTransactionID());
	}

	@Test
	void logWarning() throws Exception {
		GenericMessage<String> hello = helloWorldWithPayloadContext();
		warning.send(hello);
		Thread.sleep(2000);
		String out = outGateway.getQueue().poll();
		assertNotNull(out);
		logger.info(out);

		CreateEventRequest cer = (CreateEventRequest) marshaller.unmarshal(new StringSource(out));

		//assertEquals(null, cer.getDocument().getEventActivity().getPayload());
		assertEquals("WARNING", cer.getDocument().getEventActivity().getStatus().getValue());
		assertEquals("LOG", cer.getDocument().getEventActivity().getEvent());
		assertEquals("Some warning", cer.getDocument().getEventActivity().getDetail().getValue());
		assertEquals("fromPayloadContext", cer.getPayloadContext().getTransactionProfile().getGlobalTransactionID());
	}

	private static GenericMessage<String> helloWorld() {
		return new GenericMessage<>("Hello, world!");
	}

	private static GenericMessage<String> helloWorldWithPayloadContext() throws Exception {
		return new GenericMessage<>("<outer>" + getPayloadContextML(getPayloadContext()) + "</outer>");
	}

	private static GenericMessage<List<?>> json() {
		String sourceXML = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		TransformerContext context = new TransformerContext();
		Map<String, Object> data = new HashMap<>();
		data.put("key1", "SIMPLE_STRING");
		context.setData(data);
		List<Object> sources = new ArrayList<>();
		sources.add(sourceXML);
		sources.add(context);
		return new GenericMessage<>(sources);
	}

	private static ErrorMessage errorMessage() {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> origMessage = new GenericMessage<>(payload);
		MessagingException exception = new MessagingException(origMessage, new RuntimeException("test"));
		return new ErrorMessage(exception);
	}

	private static String getPayloadContextML(PayloadContext payloadContext) throws Exception {
		Result result = new StringResult();
		payloadContextMarshaller.marshal(payloadContext, result);
		return result.toString();
	}

	private static PayloadContext getPayloadContext() {
		ObjectFactory dataObjectFactory = new ObjectFactory();
		PayloadContext payloadContext = dataObjectFactory.createPayloadContext();
		BeanWrapper bw = new BeanWrapperImpl(payloadContext);
		bw.setAutoGrowNestedPaths(true);
		bw.setPropertyValue("MessageProfile.Domain", "domain");
		bw.setPropertyValue("MessageProfile.Process", "process");
		bw.setPropertyValue("MessageProfile.ServiceName", "serviceName");
		bw.setPropertyValue("MessageProfile.ServiceVersion", "version");
		bw.setPropertyValue("ApplicationProfile.AppName", "appName");
		bw.setPropertyValue("TransactionProfile.GlobalTransactionID", "fromPayloadContext");
		DateTimeType dateTime = new DateTimeType();
		dateTime.setValue(new DateTime());
		bw.setPropertyValue("TransactionProfile.TransactionDateTime", dateTime);
		return payloadContext;
	}

	/**
	 * Run as a java app to send a message to the real queue; the JUnit replaces the
	 * JMS infrastructure with a stub.
	 * 
	 * @throws Exception
	 */
	public static void main(String[] args) throws Exception {
		// System.setProperty("spring.profiles.active", "standalone,
		// activityMonitorCanSuppressPayload");
		logger.info("ttttt");
		System.setProperty("spring.profiles.active", "devWithJMS"); // local properties
		System.setProperty("application.configuration.name", "activity-monitor-test-client");
		System.setProperty("cic.process.id", "activity-monitor-test-process");
		createPcMarshaller();
		ConfigurableApplicationContext ctx = new ClassPathXmlApplicationContext(
				"classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml",
				"classpath:com/emc/it/eis/activity/test-flow-context.xml");
		MessageChannel inbound = ctx.getBean("inbound", MessageChannel.class);
		inbound.send(helloWorld());
		/*
		 * inbound.send(json()); inbound.send(errorMessage());
		 */MessageChannel warning = ctx.getBean("warning", MessageChannel.class);
		warning.send(helloWorldWithPayloadContext());

// 		MessageChannel chan = ctx.getBean("cic.activity.monitor.out", MessageChannel.class);
// 		chan.send(new GenericMessage<String>("junk"));

		ctx.close();
	}
}

package com.emc.it.eis.activity.restclient;

import java.util.HashMap;
import java.util.Map;

public class ActmonEventBean {

	private Map<String, String> activityAttributes = new HashMap<>();
	private Map<String, String> headerAttributes = new HashMap<>();
	private Map<String, Boolean> activityLoggingAttributes = new HashMap<>();

	public Map<String, Boolean> getActivityLoggingAttributes() {
		return activityLoggingAttributes;
	}

	public void setActivityLoggingAttributes(Map<String, Boolean> activityLoggingAttributes) {
		this.activityLoggingAttributes = activityLoggingAttributes;
	}

	public void setHeaderExpressions(Map<String, String> headerExpressions) {
		setHeaderAttributes(headerExpressions);
	}

	public void setActivityExpressions(Map<String, String> activityExpressions) {
		setActivityAttributes(activityExpressions);
	}

	public Map<String, String> getActivityAttributes() {
		return activityAttributes;
	}

	public void setActivityAttributes(Map<String, String> activityAttributes) {
		this.activityAttributes = activityAttributes;
	}

	public Map<String, String> getHeaderAttributes() {
		return headerAttributes;
	}

	public void setHeaderAttributes(Map<String, String> headerAttributes) {
		this.headerAttributes = headerAttributes;
	}

}

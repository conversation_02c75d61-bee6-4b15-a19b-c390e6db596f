/**
 * 
 */
package com.emc.it.eis.activity.config.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Configuration
@ComponentScan
public class ConfigUtil {

	@Value("${actmon.user.token}")
	private String actmonUserToken;	
	
	@Value("${unit.test.key:localhost:12345}")
	private String unitTestKey;	
	
	private static final String KRB5_CONF_KEY = "java.security.krb5.conf";
	
	public static final String CLASS_PATH_SEPERATOR = "classpath:";

	/**
	 * Creating the temp keytab file by reading file from Git repo.
	 * @param extension TODO
	 * 
	 * @return
	 * @throws IOException
	 */
	public String tempFileConfigPath(String key, String extension) throws IOException {
		log.info("ConfigUtil :: tempKeytabConfigPath() - Start");

		String tmpFileLocation = StringUtils.EMPTY;

		if (isUnitTestKey(key)) {
			tmpFileLocation = "temp";
		} else {
			// make actual REST call to get the config location

			RestTemplate restTemplate = new RestTemplate();

			List<MediaType> mediaType = new ArrayList<>();
			mediaType.add(MediaType.ALL);

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.setAccept(mediaType);
			headers.add("PRIVATE-TOKEN", actmonUserToken);

			HttpEntity<HttpHeaders> requestEntity = new HttpEntity<>(headers);

			ResponseEntity<byte[]> response = null;

			try {
				response = restTemplate.exchange(key, HttpMethod.GET, requestEntity, byte[].class);
				File tmpFile = Files.createTempFile("tmpFile", extension).toFile();
				Files.write(tmpFile.toPath(), response.getBody());
				tmpFileLocation = tmpFile.getAbsolutePath().replace("\\", "\\\\");
			} catch (Exception ex) {
				log.error("Error while calling Gitlab to get the file with key = {} :::: {}", key, ex.getMessage(), ex);
			}
		}

		log.info("tmpFileLocation for key={} location = {}", key, tmpFileLocation);

		log.info("ConfigUtil :: tempKeytabConfigPath() - End");
		return tmpFileLocation;
	}
	
	private boolean isUnitTestKey(String key)
	{
		boolean isUnitTestKey = false;
		if(!ObjectUtils.isEmpty(key) && key.contains(unitTestKey))
		{
			isUnitTestKey = true;
		}
		return isUnitTestKey;
	}

}

package com.emc.it.eis.activity.restclient;

import java.util.Map;

/**
 * <code>ActmonLogger</code> interface having different methods to log an
 * activity event.
 * 
 * <AUTHOR>
 *
 */

public interface ActmonLogger {

	/**
	 * This method logs start activity event to activity transaction database.
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param eventCode
	 * @param properties
	 */
	public void logStartEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, String eventCode, Map<String, Object> properties);

	/**
	 * This method logs complete activity event.
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param eventCode
	 * @param properties
	 */
	public void logCompleteEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, String eventCode, Map<String, Object> properties);

	/**
	 * This method logs terminate activity event. Caller of this method needs to
	 * pass all parameter along with <code>cause</code>.
	 * <p>
	 * value of cause is given to specify reason of termination.
	 * </p>
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param cause
	 * @param eventCode
	 * @param properties
	 */
	public void logTerminateEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, String cause, String eventCode, Map<String, Object> properties);

	/**
	 * This method is used to log failed activity event.
	 * <p>
	 * value of <code>detail</code> need to be given as reason of failure.
	 * </p>
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param detail
	 * @param eventCode
	 * @param properties
	 */
	public void logFailedEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, Object detail, String eventCode, Map<String, Object> properties);

	/**
	 * This method logs warn activity event.
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param properties
	 */
	public void logWarningEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, Map<String, Object> properties);

	/**
	 * This method logs information activity event.
	 * 
	 * @param businessIdentifier
	 * @param alternetBusinessId
	 * @param globalTransactionId
	 * @param step
	 * @param payload
	 * @param properties
	 * 
	 */
	public void logInfoEvent(String businessIdentifier, String alternetBusinessId, String globalTransactionId,
			String step, Object payload, Map<String, Object> properties);

}

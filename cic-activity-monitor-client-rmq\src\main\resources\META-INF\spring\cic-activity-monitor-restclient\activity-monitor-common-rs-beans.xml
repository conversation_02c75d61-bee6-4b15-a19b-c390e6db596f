<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<context:component-scan base-package="com.emc.it.eis.activity.restclient"/>
	<context:property-placeholder/>

	<util:map id="activityExpressions">
		<entry key="Document.EventActivity.Event"				value="PROCESS" />
		<entry key="Document.EventActivity.EventSubCode"		value="" />
	</util:map>

	<util:map id="headerExpressions">
		<entry key="PayloadContext.MessageProfile.Domain"			value="${am.application.domain:ERP}" />
		<entry key="PayloadContext.MessageProfile.Process"			value="${cic.process.id:UNDEFINED}" />
		<entry key="PayloadContext.MessageProfile.ServiceName"		value="${am.serviceName:UNDEFINED}" />
		<entry key="PayloadContext.MessageProfile.ServiceVersion"	value="${am.serviceVersion:UNDEFINED}" />
		
		<entry key="PayloadContext.TransactionProfile.Event"		value="PROCESS"/>
		<entry key="PayloadContext.TransactionProfile.RepostFlag"	value="false"/>
		<entry key="PayloadContext.TransactionProfile.TransactionMode"	value="${am.transactionMode:}"/>
		
		<entry key="PayloadContext.ApplicationProfile.AppName"		value="${application.configuration.name:UNDEFINED}" />
	</util:map>
	
	<util:map id="activityLoggingExpressions">
		<entry key="actmonLoggingEnabled" value="${actmon.logging.flag:true}" />
		<entry key="actmonStartEnabled" value="${actmon.start.logging.flag:true}" />
		<entry key="actmonTerminatedEnabled" value="${actmon.terminate.logging.flag:true}" />
		<entry key="actmonFailedEnabled" value="${actmon.failed.logging.flag:true}" />
		<entry key="actmonInfoEnabled" value="${actmon.info.logging.flag:true}" />
		<entry key="actmonWarningEnabled" value="${actmon.warning.logging.flag:true}" />
		<entry key="actmonCompleteEnabled" value="${actmon.complete.logging.flag:true}" />
	</util:map>

	<!-- Bean for cic Actmon Activity Data-->
	<bean id="activityBean" scope="singleton" class="com.emc.it.eis.activity.restclient.ActmonEventBean" >
		<property name="headerExpressions" ref="headerExpressions" />
		<property name="activityExpressions" ref="activityExpressions" />
		<property name="activityLoggingAttributes" ref="activityLoggingExpressions" />
	</bean>
	
	<bean id="actmonLoggerImpl" class="com.emc.it.eis.activity.restclient.ActmonLoggerImpl" />
	
	<bean id="objectMapper" class="com.emc.it.eis.activity.mapper.CustomMapper" />
	
</beans>

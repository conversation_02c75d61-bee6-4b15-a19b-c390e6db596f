package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.amqp.RabbitHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.activity.restclient.ActmonRestClient;

@SpringJUnitConfig(locations = "classpath:/com/emc/it/eis/activity/RestClientPropertiesLayer7Test-context.xml")
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client-layer7.properties", "classpath:cic-activity-monitor-client.properties"})
@EnableAutoConfiguration(exclude = {RabbitHealthContributorAutoConfiguration.class})
@ActiveProfiles("test")
class RestClientPropertiesLayer7Test 
{

	@Autowired
	ActmonRestClient restClient;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER")	
	OAuth2AuthorizedClientManager authorizedClientManager;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_OAUTH2")
	RestTemplate restTemplate;

	@Test
	void test() {
		assertNotNull(restClient.getRestTemplate());
		assertNotNull(restClient.getClientProperties());
		assertEquals("https://localhost/cic-activity-monitor-webservice/actmon.ws",
				restClient.getClientProperties().getUrl());
		assertEquals("testKey", restClient.getClientProperties().getApiKey());
		assertNotNull(0);
		assertTrue(restClient.getClientProperties().getUrl().contains("https"));
	}

}

package com.emc.it.eis.activity.mapper;

import java.io.Serial;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

public class CustomMapper extends ObjectMapper {
	@Serial
	private static final long serialVersionUID = 1L;

	public CustomMapper() {
		JodaModule jodaModule = new JodaModule();
		JavaTimeModule javaTimeModule = new JavaTimeModule();
		this.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
		this.enable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
		this.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		this.registerModule(jodaModule);
		this.registerModule(javaTimeModule);
	}

	@Override
	public CustomMapper copy() {
		// TODO Auto-generated method stub
		return new CustomMapper();
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:int="http://www.springframework.org/schema/integration"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:component-scan base-package="com.emc.it.eis.activity.restclient,com.emc.it.eis.oauth2client">
		<context:exclude-filter type="regex" expression="com.emc.it.eis.activity.restclient.ActmonLoggerImpl" />
	</context:component-scan>
	<context:property-placeholder/>

	<beans profile="default,standalone">
		<import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-beans.xml" />
		<import resource="classpath:META-INF/spring/cic-config/cic-common-activity-monitor-bean-config.xml" />
		<import resource="classpath:META-INF/spring/cic-activity-monitor-client/activity-monitor-flows.xml" />
		<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-client-beans.xml" />
<!--		<bean id="activityMonitorReloadableProperties" class="com.emc.it.eis.activity.ReloadablePropertiesImpl" />-->
		<bean id="baseActivityMonitorInterceptor" abstract="true" class="com.emc.it.eis.activity.restclient.ws.ActivityInterceptorREST">
			<constructor-arg ref="eventMarshaller" />
			<constructor-arg ref="cic.activity.monitor.wsclient.transactionalActivitySender" />
			<constructor-arg>
				<map>
					<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
				</map>
			</constructor-arg>
<!--			<constructor-arg ref="activityMonitorReloadableProperties" />-->
			<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
		</bean>

		<bean id="basePayloadActivityMonitorInterceptor" abstract="true" class="com.emc.it.eis.activity.restclient.ws.ActivityInterceptorREST">
			<constructor-arg ref="eventMarshaller" />
			<constructor-arg ref="cic.activity.monitor.wsclient.transactionalActivitySender" />
			<constructor-arg>
				<map>
					<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
					<entry key="Payload" value="payload" />
				</map>
			</constructor-arg>
<!--			<constructor-arg ref="activityMonitorReloadableProperties" />-->
			<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
		</bean>

		<bean id="cic.activity.monitor.wsclient.transactionalActivitySender"
			class="com.emc.it.eis.activity.restclient.ws.ActivityInterceptorREST$TransactionalActivitySenderImpl" />

		<bean id="eventMarshaller" class="org.springframework.oxm.jaxb.Jaxb2Marshaller">
			<property name="contextPath" value="com.emc.it.enterprise.data.v1" />
		</bean>

		<bean id="actmonLoggingProperties" class="com.emc.it.eis.activity.ActmonLoggingProperties">
			<property name="actmonLoggingEnabled" value="${actmon.logging.flag:true}" />
			<property name="actmonFlow" value="${actmon.flow:}" />
			<property name="actmonStartEnabled" value="${actmon.start.logging.flag:true}" />
			<property name="actmonTerminatedEnabled" value="${actmon.terminate.logging.flag:true}" />
			<property name="actmonFailedEnabled" value="${actmon.failed.logging.flag:true}" />
			<property name="actmonInfoEnabled" value="${actmon.info.logging.flag:true}" />
			<property name="actmonWarningEnabled" value="${actmon.warning.logging.flag:true}" />
			<property name="actmonCompleteEnabled" value="${actmon.complete.logging.flag:true}" />
		</bean>
		
		<bean id="rulesConfigurationProperties" class="com.emc.it.eis.activity.RulesConfigurationProperties">
			<property name="rulesConfigurationEnabled" value="${rules.configuration.flag:true}" />
			<property name="rulesStartEnabled" value="${rules.start.configuration.flag:false}" />
			<property name="rulesTerminatedEnabled" value="${rules.terminate.configuration.flag:false}" />
			<property name="rulesFailedEnabled" value="${rules.failed.configuration.flag:true}" />
			<property name="rulesInfoEnabled" value="${rules.info.configuration.flag:false}" />
			<property name="rulesWarningEnabled" value="${rules.warning.configuration.flag:false}" />
			<property name="rulesCompleteEnabled" value="${rules.complete.configuration.flag:false}" />
		</bean>
	</beans>

</beans>
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:int="http://www.springframework.org/schema/integration"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <context:component-scan base-package="com.emc.it.eis.activity"/>
    <context:property-placeholder location="classpath:cic-activity-monitor-client.properties"/>

    <import resource="activity-monitor-common-rs-context.xml"/>

    <bean id="baseActmonLoggerImpl" class="com.emc.it.eis.activity.ActmonLoggerImpl"/>

    <bean id="clientPropertyBean" class="com.emc.it.eis.activity.restclient.RestClientProperties">
        <constructor-arg index="0" name="host" value="${cic.actmon.host}"/>
        <constructor-arg index="1" name="port" value="${cic.actmon.port}"/>
        <constructor-arg index="2" name="contextPath" value="${cic.actmon.context.path}"/>
        <constructor-arg index="3" name="httpType" value="${cic.actmon.http.type:http}"/>

        <property name="wsAvailable" value="${cic.actmon.ws.available:true}"/>
        <property name="apiKey" value="${cic.actmon.ws.apikey:}"/>
    </bean>
    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
        <property name="requestFactory" ref="httpRestClientFactory"/>
    </bean>
</beans>

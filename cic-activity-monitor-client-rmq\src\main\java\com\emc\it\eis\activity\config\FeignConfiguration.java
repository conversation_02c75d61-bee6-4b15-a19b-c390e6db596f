/*
 * package com.emc.it.eis.activity.config;
 * 
 * import org.springframework.beans.factory.annotation.Value; import
 * org.springframework.context.annotation.Bean; import
 * org.springframework.context.annotation.Configuration;
 * 
 * import feign.Logger; import feign.auth.BasicAuthRequestInterceptor;
 * 
 * 
 * @Configuration public class FeignConfiguration {
 * 
 * @Value("${actmon.custom.security.user.name}") private String user;
 * 
 * @Value("${actmon.custom.security.user.password}") private String password;
 * 
 * @Bean public BasicAuthRequestInterceptor basicAuthRequestInterceptor() {
 * return new BasicAuthRequestInterceptor(user, password); }
 * 
 * @Bean Logger.Level feignLoggerLevel() { return Logger.Level.FULL; } }
 */







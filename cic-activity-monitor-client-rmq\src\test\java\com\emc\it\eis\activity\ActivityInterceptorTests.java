/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.matches;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.transform.Result;

import org.apache.commons.logging.Log;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.DirectFieldAccessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.xml.transform.StringResult;
import org.springframework.xml.transform.StringSource;

import com.emc.it.eis.activity.ActivityInterceptor.TransactionalSenderNoFlowControl;
import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
import com.emc.it.eis.common.integration.CICHeaders;
//import com.emc.it.eis.contivo.integration.transformer.TransformerContext;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.emc.it.enterprise.data.v1.DateTimeType;
import com.emc.it.enterprise.msg.v1.ObjectFactory;
import com.emc.it.enterprise.msg.v1.PayloadContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaMapper;

import jakarta.xml.bind.Marshaller;

/**
 * Test class to test methods on {@code ActivityInterceptor} class.
 * 
 * <AUTHOR> Russell
 * <AUTHOR>
 */
@SpringJUnitConfig
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"})
class ActivityInterceptorTests {

	private static final Logger logger = LoggerFactory.getLogger(ActivityInterceptorTests.class);

	@Autowired
	private Jaxb2Marshaller eventMarshaller;

	private Jaxb2Marshaller payloadContextMarshaller;

	// @Autowired
	// private ReloadableProperties reloadableProperties = new
	// ReloadablePropertiesImpl();

	@Autowired
	@Qualifier("activityMonitorInterceptorBasicHeader")
	private ActivityInterceptor activityMonitorInterceptorBasicHeader;

	@Autowired
	@Qualifier("activityMonitorInterceptorBasicActivity")
	private ActivityInterceptor activityMonitorInterceptorBasicActivity;

	@Autowired
	@Qualifier("activityMonitorInterceptorPayloadContext")
	private ActivityInterceptor activityMonitorInterceptorPayloadContext;

	@Autowired
	@Qualifier("activityMonitorInterceptorSuppressPayload")
	private ActivityInterceptor activityMonitorInterceptorSuppressPayload;

	@Autowired
	@Qualifier("activityMonitorInterceptorJson")
	private ActivityInterceptor activityMonitorInterceptorJson;

	@Autowired
	@Qualifier("activityMonitorInterceptorException")
	private ActivityInterceptor activityMonitorInterceptorException;

	@Autowired
	@Qualifier("activityMonitorInterceptorNameValue")
	private ActivityInterceptor activityMonitorInterceptorNameValue;

	@Autowired
	@Qualifier("activityMonitorInterceptorHardFailure")
	private ActivityInterceptor activityMonitorInterceptorHardFailure;

	@Autowired
	@Qualifier("activityMonitorInterceptorHardFailureAndRecovery")
	private ActivityInterceptor activityMonitorInterceptorHardFailureAndRecovery;

	@Autowired
	@Qualifier("activityMonitorInterceptorExistingGTxAttemptOverwrite")
	private ActivityInterceptor activityMonitorInterceptorExistingGTxAttemptOverwrite;

	@Autowired
	@Qualifier("activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery")
	private ActivityInterceptor activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery;

	@Autowired
	@Qualifier("activityMonitorInterceptorChannelInNvp")
	private ActivityInterceptor activityMonitorInterceptorChannelInNvp;

	@Autowired
	@Qualifier("activityMonitorInterceptorExistingGTx")
	private ActivityInterceptor activityMonitorInterceptorExistingGTx;

	@Autowired
	@Qualifier("activityMonitorInterceptorSpELGTx")
	private ActivityInterceptor activityMonitorInterceptorSpELGTx;

	@Autowired
	@Qualifier("cic.activity.monitor.out.gateway")
	private OutGateway outGateway;

	@Autowired
	@Qualifier("hardFailureGateway")
	private OutGateway hardFailureGateway;

	@Autowired
	@Qualifier("hardFailureAndRecoveryGateway")
	private OutGateway hardFailureAndRecoveryGateway;

	@Autowired
	@Qualifier("failureAndRecoveryFailureAndRecoveryGateway")
	private OutGateway failureAndRecoveryFailureAndRecoveryGateway;

	@Autowired
	@Qualifier("activityMonitorInterceptorNoStartLogging")
	private ActivityInterceptor activityMonitorInterceptorNoStartLogging;

	@Autowired
	@Qualifier("activityMonitorInterceptorNoLogging")
	private ActivityInterceptor activityMonitorInterceptorNoLogging;	

	@Autowired
	private TransactionalSenderNoFlowControl sender;
	
	@MockBean
	private ActivityService activityService;

	@MockBean
	private AsyncHelper asyncHelper;

	private Map<String, String> headerExpressions = new HashMap<>();

	private Map<String, String> activityAttributes = new HashMap<>();

	private Map<String, String> activityExpressions = new HashMap<>();

	private ObjectMapper objectMapper = new JodaMapper().configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
			true);

	static class TransformerContext {
		private Map<String, Object> data = new HashMap<>();

		public Map<String, Object> getData() {
			return data;
		}

		public void setData(Map<String, Object> data) {
			this.data = data;
		}
	}

	@BeforeEach
	void setup() throws Exception {
		payloadContextMarshaller = new Jaxb2Marshaller();
		payloadContextMarshaller.setContextPath("com.emc.it.enterprise.msg.v1");
		Map<String, Object> props = new HashMap<>();
		props.put(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
		payloadContextMarshaller.setMarshallerProperties(props);
		payloadContextMarshaller.afterPropertiesSet();
	}

	@Test
	void basicHeader() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorBasicHeader.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNotNull(cer.getDocument().getEventActivity().getPayload(), "Expected payload");
		assertNotNull(cer.getDocument().getEventActivity().getHostName(), "Expected hostname");
		assertNotNull(cer.getDocument().getEventActivity().getThreadID(), "Expected thread name");
	}

	@Test
	void basicActivity() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorBasicActivity.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
	}

	@Test
	void payloadContext() throws Exception {
		PayloadContext payloadContext = getPayloadContext();
		String payload = """
				<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\
				<x:doc xmlns:x="http://gpr">\
				""" + getPayloadContextXML(payloadContext)
				+ "<x:someXML>Hello, world!</x:someXML>" + "</x:doc>";

		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorPayloadContext.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
		assertEquals(getPayloadContextXML(payloadContext), getPayloadContextXML(cer.getPayloadContext()));
	}

	@Test
	void suppressPayload() throws Exception {
		PayloadContext payloadContext = getPayloadContext();
		String payload = """
				<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\
				<x:doc xmlns:x="http://gpr">\
				""" + getPayloadContextXML(payloadContext)
				+ "<x:someXML>Hello, world!</x:someXML>" + "</x:doc>";

		Message<String> message = new GenericMessage<>(payload);

		headerExpressions.put("test", "headers.id");

		activityExpressions.put("Payload", "payload");

		activityExpressions.put("PayloadContext.MessageProfile.Domain", "'domain'");

		Log logger = mock(Log.class);
		DirectFieldAccessor dfa = new DirectFieldAccessor(activityMonitorInterceptorSuppressPayload);
		dfa.setPropertyValue("logger", logger);

		Message<?> mOut = activityMonitorInterceptorSuppressPayload.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		//verify(logger).info("Payload suppressed in activity event");

		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNotNull(cer.getDocument().getEventActivity().getPayload());
		assertEquals(getPayloadContextXML(payloadContext), getPayloadContextXML(cer.getPayloadContext()));
	}

	@Test
	void json() throws Exception {
		String sourceXML = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		TransformerContext context = new TransformerContext();
		Map<String, Object> data = new HashMap<>();
		data.put("key1", "SIMPLE_STRING");
		context.setData(data);
		List<Object> sources = new ArrayList<>();
		sources.add(sourceXML);
		sources.add(context);
		GenericMessage<List<?>> message = new GenericMessage<>(sources);

		Message<?> mOut = activityMonitorInterceptorJson.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		StringWriter writer = new StringWriter();
		this.objectMapper.writeValue(writer, sources);
		String expected = writer.toString();
		//assertEquals(expected, cer.getDocument().getEventActivity().getPayload());
	}

	@Test
	void exception() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> origMessage = new GenericMessage<>(payload);
		MessagingException exception = new MessagingException(origMessage, new RuntimeException("test"));
		ErrorMessage message = new ErrorMessage(exception);

		Message<?> mOut = activityMonitorInterceptorException.preSend(message, null);
		assertEquals(origMessage.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
	}

	@Test
	void nameValue() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorNameValue.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
		List<NameValuePair> nvps = cer.getDocument().getEventActivity().getNameValuePairs().getNameValuePairs();
		boolean foundNvp = false;
		for (NameValuePair nvp : nvps) {
			if ("someName".equals(nvp.getName()) && "someValue".equals(nvp.getValue())) {
				foundNvp = true;
			}
		}
		assertTrue(foundNvp);
	}

	@Test
	void hardFailure() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		try {
			activityMonitorInterceptorHardFailure.preSend(message, null);
			Thread.sleep(2000);
			// fail("Expected Exception");
		} catch (Exception e) {
		}

		String log = hardFailureGateway.getQueue().poll();
		assertNull(log);
	}

	@Test
	void failureAndRecovery() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorHardFailureAndRecovery.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		String log = hardFailureAndRecoveryGateway.getQueue().poll();
		assertNull(log);

		hardFailureAndRecoveryGateway.setFailSend(false);
		DirectChannel channel = new DirectChannel();
		channel.setComponentName("my.channel");
		mOut = activityMonitorInterceptorHardFailureAndRecovery.preSend(message, channel);
		Thread.sleep(2000);
		log = hardFailureAndRecoveryGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals(Event.PROCESS.toString(), cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("my.channel", cer.getDocument().getEventActivity().getStep().getValue());
		Thread.sleep(2000);
		log = hardFailureAndRecoveryGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals(Event.LOG.toString(), cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("1 event(s) missed", cer.getDocument().getEventActivity().getDetail().getValue());
	}

	@Test
	void failureAndRecoveryFailureAndRecovery() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Log logger = mock(Log.class);
		DirectFieldAccessor dfa = new DirectFieldAccessor(
				activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery);
		dfa.setPropertyValue("logger", logger);

		DirectChannel channel = new DirectChannel();
		channel.setComponentName("my.channel");

		failureAndRecoveryFailureAndRecoveryGateway.setFailSend(true);
		Message<?> mOut = activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery.preSend(message, channel);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		Thread.sleep(2000);
		verify(logger).error(matches("Failed to store activity event.*"), any(RuntimeException.class));

		String log = failureAndRecoveryFailureAndRecoveryGateway.getQueue().poll();
		assertNull(log);

		// The real log should succeed, but the marker event will fail.
		failureAndRecoveryFailureAndRecoveryGateway.setFailAfter(1);

		mOut = activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery.preSend(message, channel);
		Thread.sleep(2000);
		log = failureAndRecoveryFailureAndRecoveryGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals(Event.PROCESS.toString(), cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("my.channel", cer.getDocument().getEventActivity().getStep().getValue());
		Thread.sleep(2000);
		log = failureAndRecoveryFailureAndRecoveryGateway.getQueue().poll();
		assertNull(log);

		verify(logger).error(matches("Failed to store failure recovery event.*"), any(RuntimeException.class));

		failureAndRecoveryFailureAndRecoveryGateway.setFailSend(false);
		mOut = activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery.preSend(message, channel);
		Thread.sleep(2000);
		log = failureAndRecoveryFailureAndRecoveryGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals(Event.PROCESS.toString(), cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("my.channel", cer.getDocument().getEventActivity().getStep().getValue());
		Thread.sleep(2000);
		log = failureAndRecoveryFailureAndRecoveryGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals(Event.LOG.toString(), cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("2 event(s) missed", cer.getDocument().getEventActivity().getDetail().getValue());

		verify(logger, times(2)).error(anyString(), any(RuntimeException.class));
	}

	@Test
	void channelInNvp() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		DirectChannel channel = new DirectChannel();
		channel.setComponentName("my.channel");
		Message<?> mOut = activityMonitorInterceptorChannelInNvp.preSend(message, channel);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
		assertEquals("my step", cer.getDocument().getEventActivity().getStep().getValue());

		List<NameValuePair> nvps = cer.getDocument().getEventActivity().getNameValuePairs().getNameValuePairs();
		boolean foundNvp = false;
		for (NameValuePair nvp : nvps) {
			if ("channel".equals(nvp.getName()) && "my.channel".equals(nvp.getValue())) {
				foundNvp = true;
			}
		}
		assertTrue(foundNvp);
	}

	@Test
	void existingGTx() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = MessageBuilder.withPayload(payload)
				.setHeader(CICHeaders.GLOBAL_TRANSACTION_ID, "myGTx").build();

		Message<?> mOut = activityMonitorInterceptorExistingGTx.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals("myGTx", mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
	}

	@Test
	void spELGTx() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = MessageBuilder.withPayload(payload).build();

		Message<?> mOut = activityMonitorInterceptorSpELGTx.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals("SpEL", mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
	}

	@Test
	void existingGTxAttemptOverwrite() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = MessageBuilder.withPayload(payload)
				.setHeader(CICHeaders.GLOBAL_TRANSACTION_ID, "myGTx").build();

		Log logger = mock(Log.class);
		DirectFieldAccessor dfa = new DirectFieldAccessor(activityMonitorInterceptorExistingGTxAttemptOverwrite);
		dfa.setPropertyValue("logger", logger);

		Message<?> mOut = activityMonitorInterceptorExistingGTxAttemptOverwrite.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals("myGTx", mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));

		verify(logger).error("Message already has Global Tx ID header (myGTx) " + "attempt to set to (over) ignored");
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		assertNull(cer.getDocument().getEventActivity().getPayload(), "Expected no payload");
	}

	@Test
	void payloadContextGTx() throws Exception {
		PayloadContext payloadContext = getPayloadContext();
		String payload = """
				<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\
				<x:doc xmlns:x="http://gpr">\
				""" + getPayloadContextXML(payloadContext)
				+ "<x:someXML>Hello, world!</x:someXML>" + "</x:doc>";

		Message<String> message = new GenericMessage<>(payload);

		headerExpressions.put("test", "headers.id");

		activityExpressions.put("Payload", "payload");

		activityExpressions.put("PayloadContext.MessageProfile.Domain", "'domain'");

		activityMonitorInterceptorBasicHeader.setHeaderExpressions(headerExpressions);
		activityMonitorInterceptorBasicHeader.setActivityAttributes(activityAttributes);
		activityMonitorInterceptorBasicHeader.setActivityExpressions(activityExpressions);

		Message<?> mOut = activityMonitorInterceptorBasicHeader.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals("fromPayloadContext", mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
		assertEquals(getPayloadContextXML(payloadContext), getPayloadContextXML(cer.getPayloadContext()));
	}

	@Test
	void payloadContextGTxMisMatch() throws Exception {
		PayloadContext payloadContext = getPayloadContext();
		String payload = """
				<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\
				<x:doc xmlns:x="http://gpr">\
				""" + getPayloadContextXML(payloadContext)
				+ "<x:someXML>Hello, world!</x:someXML>" + "</x:doc>";

		Message<String> message = MessageBuilder.withPayload(payload)
				.setHeader(CICHeaders.GLOBAL_TRANSACTION_ID, "myGTx").build();

		headerExpressions.put("test", "headers.id");

		activityExpressions.put("Payload", "payload");

		activityExpressions.put("PayloadContext.MessageProfile.Domain", "'domain'");

		activityMonitorInterceptorBasicHeader.setHeaderExpressions(headerExpressions);
		activityMonitorInterceptorBasicHeader.setActivityAttributes(activityAttributes);
		activityMonitorInterceptorBasicHeader.setActivityExpressions(activityExpressions);

		Log logger = mock(Log.class);
		DirectFieldAccessor dfa = new DirectFieldAccessor(activityMonitorInterceptorBasicHeader);
		dfa.setPropertyValue("logger", logger);

		Message<?> mOut = activityMonitorInterceptorBasicHeader.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals("myGTx", mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));

		verify(logger)
				.error("Global Tx ID in Header (myGTx) is different " + "to payload context (fromPayloadContext)");
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNotNull(log);
		logger.info(log);

		CreateEventRequest cer = (CreateEventRequest) eventMarshaller.unmarshal(new StringSource(log));

		assertEquals("PROCESS", cer.getDocument().getEventActivity().getEvent());
		//assertEquals(payload, cer.getDocument().getEventActivity().getPayload());
		assertEquals(getPayloadContextXML(payloadContext), getPayloadContextXML(cer.getPayloadContext()));
	}

	@Test
	void noLogging() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorNoLogging.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNull(log);
	}

	@Test
	void noStartLogging() throws Exception {
		String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><someXML>Hello, world!</someXML>";
		Message<String> message = new GenericMessage<>(payload);

		Message<?> mOut = activityMonitorInterceptorNoStartLogging.preSend(message, null);
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get("test"));
		assertEquals(message.getHeaders().getId().toString(), mOut.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID));
		Thread.sleep(2000);
		String log = outGateway.getQueue().poll();
		assertNull(log);
	}

	private String getPayloadContextXML(PayloadContext payloadContext) throws Exception {
		Result result = new StringResult();
		payloadContextMarshaller.marshal(payloadContext, result);
		return result.toString();
	}

	private PayloadContext getPayloadContext() {
		ObjectFactory dataObjectFactory = new ObjectFactory();
		PayloadContext payloadContext = dataObjectFactory.createPayloadContext();
		BeanWrapper bw = new BeanWrapperImpl(payloadContext);
		bw.setAutoGrowNestedPaths(true);
		bw.setPropertyValue("MessageProfile.Domain", "domain");
		bw.setPropertyValue("MessageProfile.Process", "process");
		bw.setPropertyValue("MessageProfile.ServiceName", "serviceName");
		bw.setPropertyValue("MessageProfile.ServiceVersion", "version");
		bw.setPropertyValue("ApplicationProfile.AppName", "appName");
		bw.setPropertyValue("TransactionProfile.GlobalTransactionID", "fromPayloadContext");
		DateTimeType dateTime = new DateTimeType();
		dateTime.setValue(new DateTime());
		bw.setPropertyValue("TransactionProfile.TransactionDateTime", dateTime);
		return payloadContext;
	}

	public static final class SuppressingReloadablePropertiesImpl {

		public String getActivityMonitorSuppressPayload() {
			return "true";
		}
	}
}

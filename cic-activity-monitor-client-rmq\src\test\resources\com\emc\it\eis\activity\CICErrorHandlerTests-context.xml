<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
    xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:util="http://www.springframework.org/schema/util"
    xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
 
    <import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml"/>
    
  <!-- override to speed things up -->
 <int-flow:flow id="jms-error-handler" flow-id="cic-default-error-handler">
        <props>
            <prop key="deliveryCountHeaderName">JMSXDeliveryCount</prop>
            <prop key="delayTime">10</prop>
        </props>
    </int-flow:flow>
    
    <int:channel id="myErrorChannel"/>
    
    <int-flow:outbound-gateway input-channel="myErrorChannel" flow="jms-error-handler"/>
 
</beans>

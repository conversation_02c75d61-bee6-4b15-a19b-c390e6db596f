package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.stream.XMLStreamException;

import org.junit.jupiter.api.Disabled;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.integration.test.support.AbstractRequestResponseScenarioTests;
import org.springframework.integration.test.support.PayloadValidator;
import org.springframework.integration.test.support.RequestResponseScenario;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import com.emc.it.eis.common.integration.CICHeaders;
import com.emc.it.eis.common.xml.XMLStreamReaderTemplate;

@Disabled
@SpringJUnitConfig
public class ActivityMonitorFlowTests extends AbstractRequestResponseScenarioTests {
	static {
		System.setProperty("spring.profiles.active", "dev");
	}

	@Override
	protected List<RequestResponseScenario> defineRequestResponseScenarios() {
		List<RequestResponseScenario> scenarios = new ArrayList<>();

		String globalTransactionId = UUID.randomUUID().toString();

		Map<String, Object> headers = Map.of(CICHeaders.GLOBAL_TRANSACTION_ID, (Object) globalTransactionId);

		Message<String> msg = MessageBuilder.withPayload("Hello, World").copyHeaders(headers).build();

		ErrorMessage errorMsg = new ErrorMessage(new MessagingException(msg, new RuntimeException("exception")),
				headers);

		scenarios.add(scenario(msg, "TERMINATED", "inputChannel"));
		scenarios.add(scenario(errorMsg, "FAILED", "errorChannel"));
		scenarios.add(scenario(msg, "COMPLETED", "inputChannel2"));
		return scenarios;
	}

	private RequestResponseScenario scenario(final Message<?> message, final String status, final String inputChannel) {
		return new RequestResponseScenario(inputChannel, "cic.activity.monitor.out")
				.setMessage(message).setName(status).setResponseValidator(new PayloadValidator<String>() {

					@SuppressWarnings("unchecked")
					@Override
					protected void validateResponse(String response) {
						XMLStreamReaderTemplate template = new XMLStreamReaderTemplate(response);
						try {
							Map<String, String> properties = new HashMap<>();
							List<String> captureElements = Arrays
									.asList(new String[]{"Event", "Status", "Detail", "Environment", "ServiceName"});
							while (template.hasNext()) {
								if (template.nextElement()) {

									String name = null;
									String value = null;
									if (captureElements.contains(template.getLocalName())) {
										name = template.getLocalName();
										value = template.getElementText();
									} else if ("Name".equals(template.getLocalName())) {
										name = template.getElementText();
										value = template.getNextElementText("Value");
									}
									if (name != null) {
										properties.put(name, value);
									}
								}
							}
							assertEquals(status, properties.get("Status"));
							assertEquals("PROCESS", properties.get("Event"));
							if ("FAILED".equals(status)) {
								assertTrue(properties.get("Detail").startsWith(MessagingException.class.getName()));
							}

							assertEquals("flow-test-app", properties.get(CICHeaders.APPLICATION_ID));
							assertEquals("flow-test-process", properties.get(CICHeaders.PROCESS_ID));
							assertEquals(message.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID),
									properties.get(CICHeaders.GLOBAL_TRANSACTION_ID));

							assertEquals("dev", properties.get("Environment"));
							assertEquals("service", properties.get("ServiceName"));

						} catch (XMLStreamException e) {

						}
					}
				});
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:int="http://www.springframework.org/schema/integration"
    xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
		

	<context:property-placeholder location="classpath:cic-activity-monitor-client.properties" />
	<import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml"/>

	<import resource="classpath:com/emc/it/eis/activity/test-flow-context.xml"/>
	
	<!-- Override the real JMS infrastructure so we can use TestJmsOperations' BlockingQueue instead  -->
   
	<int:service-activator input-channel="cic.activity.monitor.out"
		ref="activityMonitorGateway"/>
		
	<bean id="activityMonitorGateway" class="com.emc.it.eis.activity.OutGateway"/>

	<!--<bean id="activityMonitorConnectionFactory" class="org.mockito.Mockito" factory-method="mock">
		<constructor-arg value="org.springframework.amqp.rabbit.connection.CachingConnectionFactory" />
	</bean>
	
	<bean id="activityMonitorJmsTransactionManager" class="org.mockito.Mockito" factory-method="mock">
		<constructor-arg value="org.springframework.transaction.PlatformTransactionManager"/>
	</bean>-->
		
</beans>

package com.emc.it.eis.activity;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.PollableChannel;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
import com.emc.it.eis.activity.test.AbstractRestClientRMQWithChannelCoverageTest;

import jakarta.xml.bind.Marshaller;

@SpringJUnitConfig
@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"}, properties = {"app_config_name=test-app-config",
"spring.main.allow-bean-definition-overriding=true", "spring.cloud.config.enabled=false", "ssl-truststore-location=test",
"dedup.config.service.url=http://localhost:12345",
"dedup.config.service.userName=test", "dedup.config.service.password=test", "spring.application.name=testapp"})
class ActivityInterceptorRestIntegrationTests  extends AbstractRestClientRMQWithChannelCoverageTest
{
	private Logger logger = LoggerFactory.getLogger(getClass());

	@Autowired
	@Qualifier("cic.activity.monitor.out")
	MessageChannel inbound;

	@Autowired
	@Qualifier("cic.activity.reply.back.channel")
	PollableChannel outChannel;

	@Autowired
	@Qualifier("eventMarshaller")
	Jaxb2Marshaller marshaller;
	
	@MockBean
	private ActivityService activityService;
	
	@MockBean
	private AsyncHelper asyncHelper;
	
	@MockBean
	RulesConfigurationProperties rulesConfigurationProperties;

	private static Jaxb2Marshaller payloadContextMarshaller;

	private static final String profile = "test";	

	static {
		System.setProperty("spring.profiles.active", profile);
	}

	@BeforeEach
	void setup() throws Exception {
		createPcMarshaller();
	}

	private static void createPcMarshaller() throws Exception {
		payloadContextMarshaller = new Jaxb2Marshaller();
		payloadContextMarshaller.setContextPath("com.emc.it.enterprise.msg.v1");
		Map<String, Object> props = new HashMap<>();
		props.put(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
		payloadContextMarshaller.setMarshallerProperties(props);
		payloadContextMarshaller.afterPropertiesSet();
	}

	@Test
	void simple() {
		GenericMessage<String> hello = helloWorld();
		logger.info("Message to send : {}", hello);
		inbound.send(hello);
		Message<?> outMessage = outChannel.receive(1000);
		logger.info("Received message : {}", outMessage);
	}

	private static GenericMessage<String> helloWorld() {
		return new GenericMessage<>("Hello, world!");
	}

}

package com.emc.it.eis.activity.restclient;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.emc.it.eis.activity.Status;

/**
 * Activity monitoring logger implementation class. This class collects data
 * from different methods and passes to Actmon webservice.
 * 
 * <AUTHOR>
 *
 */

//@Component
public final class ActmonLoggerImpl extends BaseActmonLoggerImpl implements ActmonLogger, ApplicationContextAware {

	private static final Logger logger = LoggerFactory.getLogger(ActmonLoggerImpl.class);

	static {
		if (System.getProperty("spring.profiles.active") == null) {
			System.setProperty("spring.profiles.active", "standalone");
		}
	}

	private static ActmonLoggerImpl actmonLoggerImpl;

	private ActmonLoggerImpl() {
	}

	public static ActmonLoggerImpl getInstance(Class<?> cls) {
		if (actmonLoggerImpl == null) {
			if (activityBean == null) {
				initialize();
			}
			actmonLoggerImpl = new ActmonLoggerImpl();
			actmonLoggerImpl.clazz = cls;
			return actmonLoggerImpl;
		}
		return actmonLoggerImpl;
	}

	/**
	 * This method processes <code>step</code> value.
	 * <p>
	 * If step value is not provided then it will collect class name where event
	 * method is invoked.
	 * </p>
	 * 
	 * @param jsonString
	 * @param step
	 * @param className
	 * @return step
	 */
	private String processStep(String step, String className) {
		String returnValue = null;
		if (StringUtils.isNotBlank(step)) {
			return step;
		} else {
			Throwable throwable = new Throwable();
			StackTraceElement[] elements = throwable.getStackTrace();
			if (elements != null && elements.length > 2) {
				returnValue = elements[2].getClassName() + "." + elements[2].getMethodName();
			}
			throwable = null;
			return returnValue;
		}
	}

	@Override
	public void logStartEvent(String bid, String altBid, String gti, String step, Object payload, String eventCode,
			Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.STARTED.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, null, null, eventCode, properties));

	}

	@Override
	public void logCompleteEvent(String bid, String altBid, String gti, String step, Object payload, String eventCode,
			Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.COMPLETED.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, null, null, eventCode, properties));

	}

	@Override
	public void logTerminateEvent(String bid, String altBid, String gti, String step, Object payload, String cause,
			String eventCode, Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.TERMINATED.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, cause, null, eventCode, properties));
	}

	@Override
	public void logFailedEvent(String bid, String altBid, String gti, String step, Object payload, Object detail,
			String eventCode, Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.FAILED.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, detail, null, eventCode, properties));
	}

	@Override
	public void logWarningEvent(String bid, String altBid, String gti, String step, Object payload,
			Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.WARNING.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, null, null, null, properties));
	}

	@Override
	public void logInfoEvent(String bid, String altBid, String gti, String step, Object payload,
			Map<String, Object> properties) {
		CompletableFuture.runAsync(() -> logEvent(Status.INFO.toString(), bid, altBid, gti,
				processStep(step, clazz.getName()), payload, null, null, null, properties));
	}

	/**
	 * Method responsible for spring initialization and loading properties from cic
	 * adm database.
	 * 
	 * @param applicationConfigurationName
	 */
	private static synchronized void initialize() {
		/*
		 * Keeping null check again, will check in second(not first) thread. and if
		 * activity bean in initialized then will skip reloading properties from
		 * property loader.
		 */
		if (activityBean == null) {
			logger.info("Initializing .....");
			if (applicationContext != null) {
				activityBean = (ActmonEventBean) applicationContext.getBean("activityBean");
			}

		}
	}

	@Override
	public void setApplicationContext(ApplicationContext appContext) throws BeansException {
		logger.info("Setting application context....");
		super.setApplicationContext(appContext);
		initialize();
	}
}

/**
 * 
 */
package com.emc.it.eis.activity.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.GZIPOutputStream;

import org.apache.kafka.common.errors.TimeoutException;
import org.joda.time.DateTime;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.emc.it.eis.activity.RulesConfigurationProperties;
import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.domain.ActvityKafka;
import com.emc.it.eis.activity.domain.RMQPayload;
import com.emc.it.eis.activity.restclient.ActmonEventBean;
import com.emc.it.eis.activity.restclient.ActmonLoggerImpl;
import com.emc.it.enterprise.data.v1.CreateEventRequest;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ActivityService {

	@Autowired
	private KafkaTemplate<Long, Map<String, Object>> kafkaTemplate;

	@Autowired
	private KafkaTemplate<Long, Map<String, Object>> failOverKafkaTemplate;

	@Autowired
	private RabbitTemplate actmonRabbitTemplate;

	@Autowired
	private RabbitTemplate actmonRabbitEventTemplate;

	@Autowired
	private RabbitTemplate payloadRabbitTemplate;

	@Autowired
	private ApplicationContext context;	
	
	@Autowired
	private RulesConfigurationProperties rulesConfigurationProperties;

	/*
	 * @Autowired private EsRepository repository;
	 */
	ActmonLoggerImpl actmonLoggerImpl = ActmonLoggerImpl.getInstance(getClass());

	@Value("${app.topic.actmon}")
	private String activityTopic;

	@Value("${actmon.kafka.broker.port}")
	private String kafkaBrokerPort;

	@Value("${failover.enabled}")
	private String failOverKey;

	@Value("${spring.kafka.bootstrap-servers}")
	private String bootstrapServers;

	@Value("${spring.kafka.failover-bootstrap-servers}")
	private String failOverBootstrapServers;

	@Value("${connections.max.idle.ms}")
	private String connMaxIdleMs;

	@Value("${request.timeout.ms}")
	private String reTimeOutMs;

	@Value("${admin.client.timeout.ms}")
	private int adminClientTimedoutMs;

	public CompletableFuture<SendResult<Long, Map<String, Object>>> sendToKafka(Map<String, Object> activityMap,
			String topicName) throws Exception {
		try {

			log.info("Default Configured Topic name  is {}", activityTopic);

			return kafkaTemplate.send(activityTopic, activityMap);

		} catch (TimeoutException e) {
			throw new Exception(e.getMessage());
		} finally {
			kafkaTemplate.flush();

		}
	}

	public CompletableFuture<SendResult<Long, Map<String, Object>>> sendToKafkaWithFailOverKafkaTemplate(
			Map<String, Object> activityMap, String topicName) throws Exception {
		try {

			log.info("Default Configured Topic name  is {}", activityTopic);

			return failOverKafkaTemplate.send(activityTopic, activityMap);

		} catch (TimeoutException e) {
			throw new Exception(e.getMessage());
		} finally {
			failOverKafkaTemplate.flush();

		}
	}

	public ActvityKafka getKafkaActivity(Activity message, String topicName) {
		ActvityKafka act = new ActvityKafka();

		act.setEvent(message.getEvent());
		act.setEventCode(message.getEventCode());
		act.setStep(message.getStep());
		act.setSummary(message.getSummary());
		act.setDetail(message.getDetail());
		act.setBusiness_identifier(message.getBusinessIdentifier());
		act.setAlternate_business_identifier(message.getAltbusinessIdentifier());
		act.setGlobal_transaction_id(message.getGlobalTransactionId());
		act.setTransactionMode(message.getTransactionMode());
		act.setDomain(message.getDomain());
		act.setEnvironment(message.getEnvironment());
		act.setService_name(message.getServiceName());
		act.setServiceVersion(message.getServiceVersion());
		act.setApp_name(message.getAppName());
		act.setAppUser(message.getAppUser());
		act.setHost_name(message.getHostname());
		act.setThread_name(Thread.currentThread().getName());

		act.setIsPayloadTempered(message.getIsPayloadTempered());

		if (!ObjectUtils.isEmpty(message.getReceiver())) {
			act.setReceiver(message.getReceiver());
		}

		UUID uuid = UUID.randomUUID();
		act.setGuid(uuid.toString().replace("-", ""));

		/*
		 * if (!StringUtils.isEmpty(topicName)) { // Payload is set for E2EV if
		 * (!StringUtils.isEmpty(message.getPayload())) {
		 * act.setPayload(message.getPayload().toString()); } } else { // Setting guid
		 * as payload metadata for support portal //
		 * act.setPayload(uuid.toString().replace("-", ""));
		 * 
		 * if (!StringUtils.isEmpty(message.getPayload())) {
		 * act.setPayload(message.getPayload().toString()); }
		 * 
		 * }
		 */

		if (!ObjectUtils.isEmpty(message.getPayload())) {
			act.setPayload(message.getPayload());
		}

		act.setStatus(message.getStatus());

		SimpleDateFormat format = getDateTimeFormat();

		if (!ObjectUtils.isEmpty(message.getDateTime())) {
			if (log.isInfoEnabled()) {

				log.info("Setting the date which is coming from component {}", message.getDateTime());
			}
			act.setDate_time(format.format(message.getDateTime().toDate()));
		} else {
			log.info("Setting the current date");

			act.setDate_time(format.format(new Date()));
		}

		return act;
	}

	public static String compress(String str) throws IOException {
		if (str == null || str.length() == 0) {
			return str;
		}

		ByteArrayOutputStream out = new ByteArrayOutputStream();
		GZIPOutputStream gzip = new GZIPOutputStream(out);
		gzip.write(str.getBytes());
		gzip.close();
		return out.toString("ISO-8859-1");
	}

	/**
	 * Get Date time based on UTC
	 * 
	 * @return
	 */
	private SimpleDateFormat getDateTimeFormat() {
		// date_time
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
		format.setTimeZone(TimeZone.getTimeZone("UTC"));
		return format;
	}

	// @Async("actmonAsyncExecutor")
	public void sendToMQ(Activity activity) throws Exception {
		actmonLoggerImpl.setActivityBean(getActivityBean(activity));
		CreateEventRequest createEventRequest = actmonLoggerImpl.prepareCreateEventRequest(activity.getStatus(),
				activity.getBusinessIdentifier(), activity.getAltbusinessIdentifier(),
				activity.getGlobalTransactionId(), activity.getStep(), activity.getPayload(), activity.getDetail(),
				activity.getSummary(), activity.getEventCode(), null);
		actmonRabbitTemplate.convertAndSend(marshalCreateEventRequest(createEventRequest));
	}

	public void processPayload(Activity activity, RMQPayload rmqPayload) {
		UUID uuid = UUID.randomUUID();
		rmqPayload.setGuid(uuid.toString().replace("-", ""));

		SimpleDateFormat format = getDateTimeFormat();

		if (!ObjectUtils.isEmpty(activity.getDateTime())) {
			if (log.isInfoEnabled()) {

				log.info("Setting the date which is coming from component {}", activity.getDateTime());
			}
			rmqPayload.setDateTime(reTimeOutMs);
		} else {
			log.info("Setting the current date");

			rmqPayload.setDateTime(reTimeOutMs);
		}

		rmqPayload.setPayload(activity.getPayload());
		rmqPayload.setAppName(activity.getAppName());
		rmqPayload.setBusinessIdentifier(activity.getBusinessIdentifier());

		processPayloadAsync(rmqPayload);
	}

	// Run in seperate thread for posting the payload to ES
	public void processPayloadAsync(RMQPayload rmqPayload) {
		ExecutorService singleThreadExectorservice = Executors.newSingleThreadExecutor();

		CompletableFuture.runAsync(() ->
			payloadRabbitTemplate.convertAndSend(marshalRMQPayload(rmqPayload)), singleThreadExectorservice);

		singleThreadExectorservice.shutdown();
	}

	void sendToMQCreateEventRequest(CreateEventRequest createEventRequest) throws Exception {

		log.info("Going to send to RMQ for actmon ws call");

		actmonRabbitTemplate.convertAndSend(marshalCreateEventRequest(createEventRequest));
	}

	// Kafka Event
	public void sendToEventMQ(Activity activity) throws Exception {
		actmonLoggerImpl.setActivityBean(getActivityBean(activity));
		CreateEventRequest createEventRequest = actmonLoggerImpl.prepareCreateEventRequest(activity.getStatus(),
				activity.getBusinessIdentifier(), activity.getAltbusinessIdentifier(),
				activity.getGlobalTransactionId(), activity.getStep(), activity.getPayload(), activity.getDetail(),
				activity.getSummary(), activity.getEventCode(), null);
		actmonRabbitEventTemplate.convertAndSend(marshalCreateEventRequest(createEventRequest));
	}

	private ActmonEventBean getActivityBean(Activity activity) {
		ActmonEventBean activityBean = new ActmonEventBean();
		Map<String, String> activityExpressions = new HashMap<>();
		activityExpressions.put("Document.EventActivity.Event", "PROCESS");
		activityExpressions.put("Document.EventActivity.EventSubCode", activity.getEventSubcode());
		activityBean.setActivityExpressions(activityExpressions);

		Map<String, String> headerExpressions = new HashMap<>();
		activityExpressions.put("PayloadContext.MessageProfile.Domain", activity.getDomain());
		activityExpressions.put("PayloadContext.MessageProfile.Process", "UNDEFINED");
		activityExpressions.put("PayloadContext.MessageProfile.ServiceName", activity.getServiceName());
		activityExpressions.put("PayloadContext.MessageProfile.ServiceVersion", activity.getServiceVersion());
		activityExpressions.put("PayloadContext.TransactionProfile.Event", activity.getEvent());
		activityExpressions.put("PayloadContext.TransactionProfile.RepostFlag", "false");
		activityExpressions.put("PayloadContext.TransactionProfile.TransactionMode", activity.getTransactionMode());
		activityExpressions.put("PayloadContext.ApplicationProfile.AppName", activity.getAppName());
		activityBean.setHeaderExpressions(headerExpressions);
		return activityBean;
	}

	private String marshalCreateEventRequest(CreateEventRequest createEventRequest) {
		try {
			StringWriter sw = new StringWriter();
			JAXBContext jaxbContext = JAXBContext.newInstance(CreateEventRequest.class);
			Marshaller marshaller = jaxbContext.createMarshaller();
			marshaller.marshal(createEventRequest, sw);
			return sw.toString();
		} catch (JAXBException e) {
			e.printStackTrace();
		}

		return null;
	}

	private String marshalRMQPayload(RMQPayload rmqPayload) {
		try {
			StringWriter sw = new StringWriter();
			JAXBContext jaxbContext = JAXBContext.newInstance(RMQPayload.class);
			Marshaller marshaller = jaxbContext.createMarshaller();
			marshaller.marshal(rmqPayload, sw);
			return sw.toString();
		} catch (JAXBException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Async
	public void processAlert(Activity activity) {
		if (log.isDebugEnabled()) {

					// Send to Event Rules Queue as well to get the message processed by alert
			// framework
			log.debug("Executing method processAlert() asynchronously with Thread name :: {}", Thread.currentThread().getName());
		}

		try {
			if (rulesConfigurationProperties.getRulesConfigurationEnabled()) {
				if (rulesConfigurationProperties.checkRulesConfigurationEnabled(activity)) {
					log.info("Before sending to event Q");
					sendToEventMQ(activity);
					log.info("After sending to event Q");
				} else {
					if (log.isInfoEnabled()) {

						log.info("rules configuration is not enabled for event type {}", activity.getStatus());
					}
				}

			} else {
				if (log.isInfoEnabled()) {

					log.info("RulesConfigurationEnabled coming as {}",
							rulesConfigurationProperties.getRulesConfigurationEnabled());
				}
			}

		} catch (Exception e) {
			log.error(
					"Error while posting Event to RabbitMQ Event Rules Queue - Business Identifier {}  and App Name as {}",
					activity.getBusinessIdentifier(), activity.getAppName());
			e.printStackTrace();
		}

	}

	public void flushTemplate(String topicName) {

		@SuppressWarnings("rawtypes")
		KafkaTemplate template = (KafkaTemplate) context.getBean("kafkaTemplate");

		@SuppressWarnings("rawtypes")
		KafkaTemplate templateE2e = (KafkaTemplate) context.getBean("e2eKafkaTemplate");

		if (!ObjectUtils.isEmpty(topicName)) {
			// Sending to kafka for the topic name configured for e2e
			log.info("Specific Topic name  is {}", topicName);
			templateE2e.flush();
		} else {
			template.flush();
		}
	}

	public DateTime processDefaultDate() throws ParseException {

		Date date = new Date();

		SimpleDateFormat format = getDateTimeFormat();

		String formattedDate = format.format(date);

		return new DateTime(format.parse(formattedDate));
	}

	public void flushFailOverTemplate(String topicName) {

		@SuppressWarnings("rawtypes")
		KafkaTemplate template = (KafkaTemplate) context.getBean("failOverKafkaTemplate");

		@SuppressWarnings("rawtypes")
		KafkaTemplate templateE2e = (KafkaTemplate) context.getBean("e2eFailOverKafkaTemplate");

		if (!ObjectUtils.isEmpty(topicName)) {
			// Sending to kafka for the topic name configured for e2e
			log.info("Specific Topic name  is {}", topicName);
			templateE2e.flush();
		} else {
			template.flush();
		}
	}

	

	public String getActivityTopic() {
		return activityTopic;
	}

	public void setActivityTopic(String activityTopic) {
		this.activityTopic = activityTopic;
	}
}

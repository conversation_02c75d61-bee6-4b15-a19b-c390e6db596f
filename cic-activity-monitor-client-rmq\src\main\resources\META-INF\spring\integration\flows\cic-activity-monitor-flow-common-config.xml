<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
	xmlns:int="http://www.springframework.org/schema/integration"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<int-flow:flow-configuration>
		<int-flow:port-mapping input-channel="inputChannel" />
	</int-flow:flow-configuration>

	<int:channel id="inputChannel">
		<int:interceptors>
			<ref bean="flowActivityMonitorInterceptor" />
		</int:interceptors>
	</int:channel>

	<int:bridge input-channel="inputChannel" output-channel="nullChannel" />

</beans>

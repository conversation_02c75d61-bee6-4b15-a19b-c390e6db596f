package com.emc.it.eis.activity.restclient;



import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.auth.CredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.client.RestTemplate;

/**
 * Class to work as client for rest web service.
 * 
 * <AUTHOR>
 *
 */
public class ActmonRestClient {
	private final Logger logger = LoggerFactory.getLogger(getClass());
	public static final String ACTMON_CLIENT_OAUTH2 = "ACTMON_CLIENT_OAUTH2";
	
	@Autowired
	@Qualifier(ACTMON_CLIENT_OAUTH2)
	RestTemplate restTemplate;

	@Autowired
	@Qualifier("clientPropertyBean")
	private final RestClientProperties clientProperties = null;

	public ActmonRestClient(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	/**
	 * Gets HttpClient with assigned CredentialsProvider
	 * 
	 * @param httpClientBuilder
	 * @param credentialsProvider
	 * @return
	 */
	public CloseableHttpClient getHttpClient(HttpClientBuilder httpClientBuilder,
			CredentialsProvider credentialsProvider) {
		return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider).build();
	}

	/**
	 * Gets rest template.
	 * 
	 */
	public RestTemplate getRestTemplate() {
		return restTemplate;
	}

	/**
	 *
	 * @return RestClientProperties
	 */
	public RestClientProperties getClientProperties() {
		return clientProperties;
	}

	/**
	 * Creates URL based on the URI passed in.
	 */
	public String createUrl(String apiPath) {
		StringBuilder sb = new StringBuilder();
		sb.append(clientProperties.getUrl());
		if (StringUtils.isNotBlank(apiPath)) {
			sb.append("/").append(apiPath);
		}
		if (logger.isInfoEnabled()) {

			logger.info("URL is .{}", sb.toString());
		}
		return sb.toString();
	}

	public String getApiKey() {
		return clientProperties.getApiKey();
	}

}

package com.emc.it.eis.activity.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doNothing;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.domain.Result;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;

class AsyncHelperTest {
	private AutoCloseable mocks;
	@Mock
	AsyncHelper asynchelper;

	@BeforeEach
	void setUp() throws Exception {
		mocks = MockitoAnnotations.openMocks(this);
	}

	@Test
	final void processKafkaActivity() throws Exception {
		DeferredResult<ResponseEntity<Result>> obj = new DeferredResult<>();

		ArgumentCaptor<String> valueCapture1 = ArgumentCaptor.forClass(String.class);
		doNothing().when(asynchelper).processKafkaActivity(Mockito.any(), Mockito.any(), valueCapture1.capture());
		asynchelper.processKafkaActivity(getActivity(), obj, "kafka");
		assertEquals("kafka", valueCapture1.getValue());
	}

	@Test
	final void processKafkaActivityDefault() throws Exception {
		DeferredResult<ResponseEntity<Result>> obj = new DeferredResult<>();

		ArgumentCaptor<String> valueCapture1 = ArgumentCaptor.forClass(String.class);
		doNothing().when(asynchelper).processKafkaActivityDefault(Mockito.any(), Mockito.any(), valueCapture1.capture());
		asynchelper.processKafkaActivityDefault(getActivity(), obj, "kafka");

		assertEquals("kafka", valueCapture1.getValue());
	}

	private Activity getActivity() {

		Activity activity = new Activity();
		activity.setAppName("test");
		activity.setAppUser("testuser");
		activity.setDetail("details");
		activity.setEvent("event");
		activity.setEventCode("event code");
		activity.setStep("step");
		activity.setSummary("summary");
		activity.setAltbusinessIdentifier("altbusinessIdentifier");
		activity.setBusinessIdentifier("businessIdentifier");
		activity.setDomain("domain");
		activity.setEnvironment("environment");
		activity.setEventSubcode("eventSubcode");
		activity.setGlobalTransactionId("1");
		activity.setHostname("hostname");
		activity.setThreadId("2");
		activity.setServiceVersion("serviceVersion");
		activity.setServiceName("serviceName");
		activity.setReceiver("kafka");
		activity.setPayload("testpayload");
		List<NameValuePair> nameValuePairs = new ArrayList<>();
		NameValuePair nameValuePair = new NameValuePair();
		nameValuePair.setName("messagingsystem");
		nameValuePair.setValue("kafka");
		nameValuePairs.add(nameValuePair);
		activity.setNameValuePairs(nameValuePairs);

		return activity;
	}

	@AfterEach
	void tearDown() throws Exception {
		mocks.close();
	}


}

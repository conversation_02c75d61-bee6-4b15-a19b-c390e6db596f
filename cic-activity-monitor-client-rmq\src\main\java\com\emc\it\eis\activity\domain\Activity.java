/**
 * 
 */
package com.emc.it.eis.activity.domain;

import java.util.List;

import org.joda.time.DateTime;

import com.emc.it.enterprise.data.v1.CreateEventRequest;

import lombok.Data;

@SuppressWarnings("unused")
@Data
public class Activity {

	/**
	 * When Event is PROCESS, the following statuses are appropriate Started,
	 * Failed, Completed, Terminated. When Event is LOG, the following codes might
	 * be Error, Info, Debug, Warn
	 */
	private String status;
	/** values should PROCESS, LOG, SAVE_POINT */
	private String event;
	private String eventCode;
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getEvent() {
		return event;
	}
	public void setEvent(String event) {
		this.event = event;
	}
	public String getEventCode() {
		return eventCode;
	}
	public void setEventCode(String eventCode) {
		this.eventCode = eventCode;
	}
	public String getEventSubcode() {
		return eventSubcode;
	}
	public void setEventSubcode(String eventSubcode) {
		this.eventSubcode = eventSubcode;
	}
	public String getStep() {
		return step;
	}
	public void setStep(String step) {
		this.step = step;
	}
	public String getSummary() {
		return summary;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public String getDetail() {
		return detail;
	}
	public void setDetail(String detail) {
		this.detail = detail;
	}
	public String getBusinessIdentifier() {
		return businessIdentifier;
	}
	public void setBusinessIdentifier(String businessIdentifier) {
		this.businessIdentifier = businessIdentifier;
	}
	public String getAltbusinessIdentifier() {
		return altbusinessIdentifier;
	}
	public void setAltbusinessIdentifier(String altbusinessIdentifier) {
		this.altbusinessIdentifier = altbusinessIdentifier;
	}
	public String getGlobalTransactionId() {
		return globalTransactionId;
	}
	public void setGlobalTransactionId(String globalTransactionId) {
		this.globalTransactionId = globalTransactionId;
	}
	public String getTransactionMode() {
		return transactionMode;
	}
	public void setTransactionMode(String transactionMode) {
		this.transactionMode = transactionMode;
	}
	public String getDomain() {
		return domain;
	}
	public void setDomain(String domain) {
		this.domain = domain;
	}
	public String getEnvironment() {
		return environment;
	}
	public void setEnvironment(String environment) {
		this.environment = environment;
	}
	public String getServiceName() {
		return serviceName;
	}
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	public String getServiceVersion() {
		return serviceVersion;
	}
	public void setServiceVersion(String serviceVersion) {
		this.serviceVersion = serviceVersion;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	public String getAppUser() {
		return appUser;
	}
	public void setAppUser(String appUser) {
		this.appUser = appUser;
	}
	public String getHostname() {
		return hostname;
	}
	public void setHostname(String hostname) {
		this.hostname = hostname;
	}
	public String getThreadId() {
		return threadId;
	}
	public void setThreadId(String threadId) {
		this.threadId = threadId;
	}
	public String getPayload() {
		return payload;
	}
	public void setPayload(String payload) {
		this.payload = payload;
	}
	public String getReceiver() {
		return receiver;
	}
	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}
	public List<CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair> getNameValuePairs() {
		return nameValuePairs;
	}
	public void setNameValuePairs(List<CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair> nameValueList) {
		this.nameValuePairs = nameValueList;
	}
	/** Component or flow task that initiated the event */
	private String eventSubcode;
	private String step;
	private String summary;
	private String detail;
	private String businessIdentifier;
	private String altbusinessIdentifier;
	private String globalTransactionId;
	private String transactionMode;
	private String domain;
	private String environment;
	private String serviceName;
	private String serviceVersion;
	private String appName;
	private String appUser;
	private String hostname;
	private String threadId;
	private String payload;
	private String receiver;
	private String isPayloadTempered;
	private List<CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair> nameValuePairs;
	
	private DateTime dateTime;
	public DateTime getDateTime() {
		return dateTime;
	}

	public void setDateTime(DateTime dateTime) {
		this.dateTime = dateTime;
	}
	
	
}

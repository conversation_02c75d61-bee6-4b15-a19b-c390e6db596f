package com.emc.it.eis.activity.restclient;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import javax.xml.transform.Result;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.xml.transform.StringResult;

import com.emc.it.eis.activity.ActivityInterceptor;
import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
import com.emc.it.enterprise.data.v1.CreateEventRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * Channel intercepter that sends an event to the activity monitoring service
 * using rest web service.
 *
 * <AUTHOR>
 */
@Slf4j
public class ActivityInterceptorREST extends ActivityInterceptor {
	private static Logger logger = LoggerFactory.getLogger(ActivityInterceptorREST.class);

	@Autowired
	private ActivityService activityService;

	@Autowired
	private AsyncHelper asyncHelper;

	@Value("${kafka.flow.enabled}")
	private String kafkaFlowEnabled;

	private TransactionalActivitySender transactionalActivitySender;

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller, TransactionalSenderNoFlowControl sender) {
		super(eventMarshaller, sender);
	}

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller,
			TransactionalActivitySender transactionalActivitySender) {
		this(eventMarshaller, transactionalActivitySender, null);
	}

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller,
			TransactionalActivitySender transactionalActivitySender, Map<String, String> activityExpressions) {
		super.eventMarshaller = eventMarshaller;
		// super.reloadableProperties = reloadableProperties;
		this.transactionalActivitySender = transactionalActivitySender;
		if (activityExpressions != null) {
			this.setActivityExpressions(activityExpressions);
		}
	}

	public void setActivityAttributes(Map<String, String> activityAttributes) {
		super.setActivityAttributes(activityAttributes);
	}

	public void setActivityExpressions(Map<String, String> activityExpressions) {
		super.setActivityExpressions(activityExpressions);
	}

	/**
	 * Overridden method to intercept a message channel
	 */
	@Override
	public Message<?> preSend(Message<?> message, MessageChannel channel) {
		CreateEventRequest createEventRequest = new CreateEventRequest();
		logger.info("Calling method to extractdata from message and prepare CreateEventRequest ..");
		message = doCommon(message, channel, createEventRequest);
		CompletableFuture.runAsync(() -> asyncPreSendRest(channel, createEventRequest));
		return message;
	}

	public void asyncPreSendRest(MessageChannel channel, CreateEventRequest createEventRequest) {
		/*
		 * Send the document.
		 */
		try {
			doSend(createEventRequest);
			if (this.failures.get() > 0) {
				recordFailures(createEventRequest, channel);
			}
		} catch (Exception e) {
			this.failures.incrementAndGet();
			logger.error("Failed to store activity event: {}", objectToJson(createEventRequest), e);
			if (failOnActivityMonitorFailure) {
				throw new MessagingException("Failure while sending message to actmon", e);
			}
		}
	}

	/**
	 * Method to send event activity data to actmon web service.
	 *
	 * @param createEventRequest
	 */
	private void doSend(CreateEventRequest createEventRequest) {
		Result result = new StringResult();
		this.eventMarshaller.marshal(createEventRequest, result);
		// this.transactionalActivitySender.sendActivity(result.toString());
		if (getActmonLoggingProperties().getActmonLoggingEnabled()) {
			if (getActmonLoggingProperties().checkActmonLogginEnabled(createEventRequest)) {				
					this.transactionalActivitySender.sendActivity(result.toString());
			}
		} else {
			if (logger.isDebugEnabled()) {

				logger.debug("Actmon is disabled for the component: {}", createEventRequest.getPayloadContext().getMessageProfile().getServiceName());
			}
		}
	}

	
	public static interface TransactionalActivitySender {
		@Transactional(propagation = Propagation.REQUIRES_NEW)
		void sendActivity(String activity);
	}

	public static class TransactionalActivitySenderImpl extends BaseActmonLoggerImpl
			implements TransactionalActivitySender {
		public void sendActivity(String activity) {
			MultiValueMap<String, Object> headers = new LinkedMultiValueMap<>();
			headers.add("Content-Type", "application/xml");
			ResponseEntity<ResponseEntity<HttpStatus>> responseEntity = super.doCallWS(activity, "", headers);
			logger.info("Response : {}", responseEntity);
		}
	}
}

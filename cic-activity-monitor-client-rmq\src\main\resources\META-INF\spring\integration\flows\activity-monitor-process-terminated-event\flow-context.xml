<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:int-flow="http://www.springframework.org/schema/integration/flow" xmlns:int="http://www.springframework.org/schema/integration"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


	<context:property-placeholder/>
	<context:annotation-config />
	
    <!--Terminated (e.g. dedup) -->
    <import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-beans.xml" />
    <import resource="../cic-activity-monitor-flow-common-config.xml"/>
    <bean id="flowActivityMonitorInterceptor" parent="payloadActivityMonitorInterceptor">
        <property name="activityAttributes">
            <map>
                <entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }"/>
                <entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).TERMINATED.toString() }"/>
            </map>
        </property>
        <property name="activityExpressions">
            <map>
                <entry key="businessIdentifier" value="headers['#{T(com.emc.it.eis.common.integration.CICHeaders).BUSINESS_IDENTIFIER}']"/>
            </map>
        </property>
    </bean>  
    
</beans>  
   


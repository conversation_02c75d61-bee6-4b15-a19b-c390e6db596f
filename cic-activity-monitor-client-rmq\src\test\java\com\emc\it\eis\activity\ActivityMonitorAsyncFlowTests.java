package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.test.context.ContextConfiguration;

import com.emc.it.eis.common.integration.CICHeaders;
import com.emc.it.eis.common.xml.XMLStreamReaderTemplate;

@ContextConfiguration
class ActivityMonitorAsyncFlowTests {
	@Autowired
	MessageChannel inputChannel;

	@Autowired
	@Qualifier("cic.activity.monitor.out")
	SubscribableChannel outputChannel;

	static {
		System.setProperty("spring.profiles.active", "dev");
	}

	@Test
	@Disabled
	void completed() {
		final AtomicInteger completedCount = new AtomicInteger();
		final AtomicInteger terminatedCount = new AtomicInteger();
		final AtomicInteger failedCount = new AtomicInteger();
		outputChannel.subscribe(message -> {
			XMLStreamReaderTemplate template = new XMLStreamReaderTemplate((String) message.getPayload());
			try {
				while (template.hasNext()) {
					if (template.nextElement()) {
						if ("Status".equals(template.getLocalName())) {
							String status = template.getElementText();
							if ("COMPLETED".equals(status)) {
								completedCount.getAndIncrement();

							} else if ("TERMINATED".equals(status)) {
								terminatedCount.getAndIncrement();
							} else if ("FAILED".equals(status)) {
								failedCount.getAndIncrement();
							}
						}
					}
				}

			} catch (Exception e) {
				e.printStackTrace();
				fail(e.getMessage());
			}
		});

		for (int i = 0; i < 15; i++) {
			String globalTransactionId = UUID.randomUUID().toString();
			Map<String, Object> headers = Map.of(CICHeaders.GLOBAL_TRANSACTION_ID, (Object) globalTransactionId);
			Message<Integer> msg = MessageBuilder.withPayload(i).copyHeaders(headers).build();
			inputChannel.send(msg);
		}

		boolean success = false;
		int waitTime = 100000;
		while (!success) {
			success = completedCount.get() == 5 && terminatedCount.get() == 5 && failedCount.get() == 5;
			if (!success) {
				try {
					Thread.sleep(1000);
					waitTime -= 1000;
					assertNotNull(0, "did not receive all messages");
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}

}

package com.emc.it.eis.activity.restclient.ws;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.xml.transform.Result;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.xml.transform.StringResult;

import com.emc.it.eis.activity.ActmonClientUtil;
import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.restclient.BaseActmonLoggerImpl;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.emc.it.enterprise.msg.v1.ApplicationProfileType;
import com.emc.it.enterprise.msg.v1.MessageProfileType;
import com.emc.it.enterprise.msg.v1.PayloadContext;
import com.emc.it.enterprise.msg.v1.TransactionProfileType;

import lombok.extern.slf4j.Slf4j;

/**
 * Channel intercepter that sends an event to the activity monitoring service
 * using rest web service.
 *
 * <AUTHOR>
 */
@Slf4j
public class ActivityInterceptorREST extends ActivityInterceptor {
	private static Logger logger = LoggerFactory.getLogger(ActivityInterceptorREST.class);	

	private TransactionalActivitySender transactionalActivitySender;

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller, TransactionalSenderNoFlowControl sender) {
		super(eventMarshaller, sender);
	}

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller,
			TransactionalActivitySender transactionalActivitySender) {
		this(eventMarshaller, transactionalActivitySender, null);
	}

	public ActivityInterceptorREST(Jaxb2Marshaller eventMarshaller,
			TransactionalActivitySender transactionalActivitySender, Map<String, String> activityExpressions) {
		super.eventMarshaller = eventMarshaller;
		// super.reloadableProperties = reloadableProperties;
		this.transactionalActivitySender = transactionalActivitySender;
		if (activityExpressions != null) {
			this.setActivityExpressions(activityExpressions);
		}
	}

	public void setActivityAttributes(Map<String, String> activityAttributes) {
		super.setActivityAttributes(activityAttributes);
	}

	public void setActivityExpressions(Map<String, String> activityExpressions) {
		super.setActivityExpressions(activityExpressions);
	}

	/**
	 * Overridden method to intercept a message channel
	 */
	@Override
	public Message<?> preSend(Message<?> message, MessageChannel channel) {
		CreateEventRequest createEventRequest = new CreateEventRequest();
		logger.info("Calling method to extractdata from message and prepare CreateEventRequest ..");
		message = doCommon(message, channel, createEventRequest);
		CompletableFuture.runAsync(() -> asyncPreSendRest(channel, createEventRequest));
		return message;
	}

	public void asyncPreSendRest(MessageChannel channel, CreateEventRequest createEventRequest) {
		/*
		 * Send the document.
		 */
		try {
			doSend(createEventRequest);
			if (this.failures.get() > 0) {
				recordFailures(createEventRequest, channel);
			}
		} catch (Exception e) {
			this.failures.incrementAndGet();
			logger.error("Failed to store activity event: {}", objectToJson(createEventRequest), e);
			if (failOnActivityMonitorFailure) {
				throw new MessagingException("Failure while sending message to actmon", e);
			}
		}
	}

	/**
	 * Method to send event activity data to actmon web service.
	 *
	 * @param createEventRequest
	 */
	private void doSend(CreateEventRequest createEventRequest) {
		Result result = new StringResult();
		this.eventMarshaller.marshal(createEventRequest, result);
		// this.transactionalActivitySender.sendActivity(result.toString());
		if (getActmonLoggingProperties().getActmonLoggingEnabled()) {
			if (getActmonLoggingProperties().checkActmonLogginEnabled(createEventRequest)) {				
					this.transactionalActivitySender.sendActivity(result.toString());
			}
		} else {
			if (logger.isDebugEnabled()) {

				logger.debug("Actmon is disabled for the component: {}", createEventRequest.getPayloadContext().getMessageProfile().getServiceName());
			}
		}
	}

	private Activity getActivity(CreateEventRequest ceRequest) throws ParseException {
		Activity activity = new Activity();

		if (ceRequest.getDocument().getEventActivity() != null) {

			if (log.isInfoEnabled()) {


				log.info("Detail is {}", ceRequest.getDocument().getEventActivity().getDetail());
			}
			if (!ObjectUtils.isEmpty(ceRequest.getDocument().getEventActivity().getDetail())) {
				activity.setDetail(ceRequest.getDocument().getEventActivity().getDetail().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Event is {}", ceRequest.getDocument().getEventActivity().getEvent());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEvent())) {
				activity.setEvent(ceRequest.getDocument().getEventActivity().getEvent());
			}

			if (log.isInfoEnabled()) {


				log.info("Event Code is {}", ceRequest.getDocument().getEventActivity().getEventCode());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEventCode())) {
				activity.setEventCode(ceRequest.getDocument().getEventActivity().getEventCode());
			}

			if (log.isInfoEnabled()) {


				log.info("Event Sub Code is {}", ceRequest.getDocument().getEventActivity().getEventSubCode());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEventSubCode())) {
				activity.setEventSubcode(ceRequest.getDocument().getEventActivity().getEventSubCode());
			}

			if (log.isInfoEnabled()) {


				log.info("Thread ID is {}", ceRequest.getDocument().getEventActivity().getThreadID());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getThreadID())) {
				activity.setThreadId(ceRequest.getDocument().getEventActivity().getThreadID());
			}

			if (log.isInfoEnabled()) {


				log.info("Alternate Business Identifier is {}",
						ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier())) {

				activity.setAltbusinessIdentifier(
						ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier());
			}

			if (log.isInfoEnabled()) {


				log.info("Business Identifier is {}", ceRequest.getDocument().getEventActivity().getBusinessIdentifier());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getBusinessIdentifier())) {
				activity.setBusinessIdentifier(ceRequest.getDocument().getEventActivity().getBusinessIdentifier());
			}

			log.info("Payload is {}");
			if (!ObjectUtils
					.isEmpty(ceRequest.getDocument().getEventActivity().getPayload())) {
				activity.setPayload(ceRequest.getDocument().getEventActivity().getPayload().toString());
			}
			else
			{
				log.info("Payload is coming empty{}");
			}

			if (log.isInfoEnabled()) {


				log.info("Status is {}", ceRequest.getDocument().getEventActivity().getStatus().getValue());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getStatus().getValue())) {
				activity.setStatus(ceRequest.getDocument().getEventActivity().getStatus().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Step is {}", ceRequest.getDocument().getEventActivity().getStep());
			}

			if (!ObjectUtils.isEmpty(ceRequest.getDocument().getEventActivity().getStep())) {
				activity.setStep(ceRequest.getDocument().getEventActivity().getStep().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Host Name is {}", ceRequest.getDocument().getEventActivity().getHostName());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getHostName())) {
				activity.setHostname(ceRequest.getDocument().getEventActivity().getHostName());
			}

			if (log.isInfoEnabled()) {


				log.info("Name Value List is {}", ceRequest.getDocument().getEventActivity().getNameValuePairs());
			}

			if (!ObjectUtils
					.isEmpty(ceRequest.getDocument().getEventActivity().getNameValuePairs())) {

				List<NameValuePair> nameValueList = ceRequest.getDocument().getEventActivity().getNameValuePairs()
						.getNameValuePairs().stream().map(nameValue -> {

							NameValuePair nameValuePair = new NameValuePair();
							nameValuePair.setName(nameValue.getName());
							nameValuePair.setValue(nameValue.getValue());
							return nameValuePair;

						}).collect(Collectors.toList());

				activity.setNameValuePairs(nameValueList);

			}

		}

		PayloadContext payloadContext = ceRequest.getPayloadContext();

		if (payloadContext != null) {
			log.info("payloadContext is {}", payloadContext);

			MessageProfileType messageProfile = payloadContext.getMessageProfile();

			if (messageProfile != null) {

				if (messageProfile.getDomain() != null) {

					if (log.isInfoEnabled()) {


						log.info("Domain is {}", messageProfile.getDomain());
					}
					activity.setDomain(messageProfile.getDomain());
				}
			}

			ApplicationProfileType applicationProfile = payloadContext.getApplicationProfile();

			if (applicationProfile != null) {

				if (applicationProfile.getAppUser() != null) {

					if (log.isInfoEnabled()) {


						log.info("App User is {}", applicationProfile.getAppUser());
					}
					activity.setAppUser(applicationProfile.getAppUser());
				}
			}

			TransactionProfileType transactionProfile = payloadContext.getTransactionProfile();

			if (transactionProfile != null) {

				if (transactionProfile.getEnvironment() != null) {

					if (log.isInfoEnabled()) {


						log.info("Environment is {}", transactionProfile.getEnvironment());
					}
					activity.setEnvironment(transactionProfile.getEnvironment().toString());
				}
			}

			if (transactionProfile != null) {

				if (transactionProfile.getGlobalTransactionID() != null) {

					if (log.isInfoEnabled()) {


						log.info("Global transaction ID is {}", transactionProfile.getGlobalTransactionID());
					}

					activity.setGlobalTransactionId(transactionProfile.getGlobalTransactionID());
				}
			}

			if (transactionProfile != null) {

				if (transactionProfile.getTransactionDateTime() != null) {

					if (log.isInfoEnabled()) {


						log.info("Transaction date time is {}", transactionProfile.getTransactionDateTime());
					}

					activity.setDateTime(transactionProfile.getTransactionDateTime().getValue());
				} else {

					log.info(
							"As the date time is coming null or empty from the request body, hence setting date time as current time");

					DateTime dateTime = ActmonClientUtil.processDefaultDate();

					activity.setDateTime(dateTime);
				}
			}

			if (applicationProfile != null) {

				if (applicationProfile.getAppName() != null) {

					if (log.isInfoEnabled()) {


						log.info("App Name is {}", applicationProfile.getAppName());
					}
					activity.setAppName(applicationProfile.getAppName());
				}
			}

		}

		return activity;
	}

	public static interface TransactionalActivitySender {
		@Transactional(propagation = Propagation.REQUIRES_NEW)
		void sendActivity(String activity);
	}

	public static class TransactionalActivitySenderImpl extends BaseActmonLoggerImpl
			implements TransactionalActivitySender {
		public void sendActivity(String activity) {
			MultiValueMap<String, Object> headers = new LinkedMultiValueMap<>();
			headers.add("Content-Type", "application/xml");
			ResponseEntity<ResponseEntity<HttpStatus>> responseEntity = super.doCallWS(activity, "", headers);
			logger.info("Response : {}", responseEntity);
		}
	}
}

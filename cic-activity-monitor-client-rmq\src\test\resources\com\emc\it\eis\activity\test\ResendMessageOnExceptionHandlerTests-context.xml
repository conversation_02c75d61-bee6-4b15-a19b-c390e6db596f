<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
    xmlns:int="http://www.springframework.org/schema/integration"
    xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml"/>
   <!-- override to speed things up -->
  <int-flow:flow id="jms-error-handler" flow-id="cic-default-error-handler">
        <props>
            <prop key="deliveryCountHeaderName">JMSXDeliveryCount</prop>
            <prop key="delayTime">1</prop>
        </props>
   </int-flow:flow>  
 
  <int:service-activator input-channel="inputChannel" >
    <bean class="com.emc.it.eis.activity.test.ResendMessageOnExceptionHandler">
        <constructor-arg ref="gateway"/>
        <constructor-arg ref="errorChannel"/>
    </bean>
  </int:service-activator>
    
    <int:gateway id="gateway" default-request-channel="to.transformer" 
    error-channel="errorChannel" 
    default-reply-timeout="0"/>
    
  
    
    <int:transformer input-channel="to.transformer" output-channel="outputChannel" expression="1/0"/>
    
    <int-flow:outbound-gateway flow="jms-error-handler" input-channel="errorChannel"/>
    
    <int:bridge input-channel="cic.activity.monitor.out" output-channel="outputChannel"/>
    <int:channel id="outputChannel"/>
</beans>

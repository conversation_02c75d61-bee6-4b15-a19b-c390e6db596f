package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import com.emc.it.eis.common.integration.CICHeaders;

@SpringJUnitConfig
@TestPropertySource(locations = "classpath:cic-activity-monitor-client.properties")
class CICErrorHandlerTests 
{
	static {
		System.setProperty("spring.profiles.active", "dev");
	}

	@Autowired
	MessageChannel errorChannel;
	
	@Autowired
	MessageChannel myErrorChannel;

	@Autowired
	@Qualifier("cic.activity.monitor.out")
	SubscribableChannel activityMonitorOutputChannel;

	@Test
	void rethrowWithNoInitialHeader() {
		final ErrorMessage errorMessage = errorMessage(0, "unknown");
		try {
			myErrorChannel.send(errorMessage);
			fail("should throw exception");

		} catch (MessagingException e) {

		}
	}

	@Test
	void discard() throws Exception {

		final AtomicInteger count = new AtomicInteger();
		final ErrorMessage errorMessage = errorMessage(5, "JMSXDeliveryCount");

		activityMonitorOutputChannel.subscribe(message ->
			count.getAndIncrement());

		myErrorChannel.send(errorMessage);
		myErrorChannel.send(errorMessage);
		Thread.sleep(3000);
		assertEquals(2, count.get());
	}

	@Test
	void exception() {

		final ErrorMessage errorMessage = errorMessage(1, "JMSXDeliveryCount");

		try {
			myErrorChannel.send(errorMessage);
			fail("should throw exception");
		} catch (MessagingException e) {

		}

	}

	private ErrorMessage errorMessage(int retryCount, String retryHeaderName) {
		Map<String, Object> headers = new HashMap<>();
		headers.put(retryHeaderName, retryCount);

		String globalTransactionId = UUID.randomUUID().toString();

		headers.put(CICHeaders.GLOBAL_TRANSACTION_ID, globalTransactionId);

		Message<?> message = MessageBuilder.withPayload("Hello").copyHeaders(headers).build();
		MessagingException me = new MessagingException(message);
		return new ErrorMessage(me);
	}

}

package com.emc.it.eis.activity.restclient;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;


@ExtendWith(MockitoExtension.class)
class ActmonRestClientTest {
	private UsernamePasswordCredentials mockUserNameCredentials = mock(UsernamePasswordCredentials.class);
	private BasicCredentialsProvider mockCredentialsProvider = mock(BasicCredentialsProvider.class);

	
	private RestTemplate restTemplate;
	
	private ActmonRestClient sut;
	private AuthScope authScope = mock(AuthScope.class);
	private CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
	
	private HttpComponentsClientHttpRequestFactory mockHttpComponentsClientHttpRequestFactory = mock(
			HttpComponentsClientHttpRequestFactory.class);

	@BeforeEach
	void setUp() throws Exception {
		restTemplate = new RestTemplate();
		restTemplate.setRequestFactory(mockHttpComponentsClientHttpRequestFactory);
		sut = new ActmonRestClient(restTemplate);
		mockCredentialsProvider.setCredentials(authScope, mockUserNameCredentials);
		mockHttpComponentsClientHttpRequestFactory.setHttpClient(httpClient);
	
	}

	@Test
	void actmonRestClientConstructor() throws Exception {
		verify(mockCredentialsProvider, times(1)).setCredentials(any(AuthScope.class),
				any(UsernamePasswordCredentials.class));
		verify(mockHttpComponentsClientHttpRequestFactory, times(1)).setHttpClient(any(CloseableHttpClient.class));
	}

	@Test
	void getHttpClient() {
		HttpClientBuilder mockHttpClientBuilder = mock(HttpClientBuilder.class);
		CloseableHttpClient mockCloseableHttpClient = mock(CloseableHttpClient.class);
		when(mockHttpClientBuilder.setDefaultCredentialsProvider(mockCredentialsProvider))
		.thenReturn(mockHttpClientBuilder);
		when(mockHttpClientBuilder.build())
				.thenReturn(mockCloseableHttpClient);

		CloseableHttpClient client = sut.getHttpClient(mockHttpClientBuilder, mockCredentialsProvider);

		verify(mockHttpClientBuilder, times(1)).build();
		assertThat(client, is(mockCloseableHttpClient));
	}
}

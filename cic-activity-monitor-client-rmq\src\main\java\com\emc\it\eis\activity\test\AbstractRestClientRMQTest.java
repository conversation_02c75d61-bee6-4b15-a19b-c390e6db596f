package com.emc.it.eis.activity.test;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Import;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizedClientRepository;
import org.springframework.web.client.RestTemplate;

@Import({TestConfiguration.class})
public abstract class AbstractRestClientRMQTest  {
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_OAUTH2")
	RestTemplate restTemplate;
	
	@MockBean
	@Qualifier("ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER")	
	OAuth2AuthorizedClientManager authorizedClientManager;	
	
	@MockBean
	OAuth2AuthorizedClientRepository oauth2AuthorizedClientRepository;
	
	@MockBean
	RestTemplateBuilder restTemplateBuilder;

	
	@MockBean
	ClientRegistrationRepository clientRegistrationRepository;
}

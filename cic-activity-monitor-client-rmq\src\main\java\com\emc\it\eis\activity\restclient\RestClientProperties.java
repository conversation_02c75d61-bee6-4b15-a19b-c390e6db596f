/*
 * Copyright 2007-2012 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.activity.restclient;

/**
 * REST client properties responsible for formation of web service endpoint url.
 * 
 * <AUTHOR>
 */
public class RestClientProperties {

	private String host;
	private String port;
	private String contextPath;
	private String httpType;
	private String apiKey;

	private Boolean wsAvailable;

	public RestClientProperties() {
	}

	public RestClientProperties(String host, String port, String contextPath, String httpType) {
		this.host = host;
		this.port = port;
		this.contextPath = contextPath;
		this.httpType = httpType;
	}

	/**
	 * Gets URL.
	 */
	public String getUrl() {
		if ("https".equals(httpType)) {
			return new StringBuffer(httpType).append("://").append(host).append("/").append(contextPath).toString();
		} else {
			return new StringBuffer(httpType).append("://").append(host).append(":").append(port).append("/")
					.append(contextPath).toString();
		}
	}

	public Boolean getWsAvailable() {
		return wsAvailable;
	}

	public void setWsAvailable(Boolean wsAvailable) {
		this.wsAvailable = wsAvailable;
	}

	public String getApiKey() {
		return apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

}

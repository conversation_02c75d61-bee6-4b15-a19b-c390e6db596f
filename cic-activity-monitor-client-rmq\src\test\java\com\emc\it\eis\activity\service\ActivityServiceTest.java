package com.emc.it.eis.activity.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.apache.kafka.common.internals.KafkaCompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.actuate.autoconfigure.amqp.RabbitHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.test.AbstractClientRMQWithChannelCoverageTest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.joda.JodaModule;

@ExtendWith(SpringExtension.class)
@SpringBootConfiguration
@ActiveProfiles({"test"})
@EnableAutoConfiguration(exclude = {RabbitHealthContributorAutoConfiguration.class, DataSourceAutoConfiguration.class, ActiveMQAutoConfiguration.class, RabbitAutoConfiguration.class, KafkaAutoConfiguration.class})
class ActivityServiceTest extends AbstractClientRMQWithChannelCoverageTest
{
	//@Autowired
	//ActivityService activityservice;
	
	@InjectMocks
	ActivityService activityservice;

	@Mock
	KafkaTemplate<Long, Map<String, Object>> kafkaTemplate;

	@Mock
	KafkaTemplate<Long, Map<String, Object>> failOverKafkaTemplate;

	@Mock
	RabbitTemplate actmonTemplate;


	@BeforeEach
	void setUp() throws Exception {
		//System.setProperty("java.security.auth.login.config", "C:\\Users\\<USER>\\git\\aic-actmon\\cic-activity-monitor-client-rmq\\src\\test\\resources\\tls\\nonprd-jaas.conf");
		//System.setProperty("java.security.krb5.conf", "C:\\Users\\<USER>\\git\\aic-actmon\\cic-activity-monitor-client-rmq\\src\\test\\resources\\tls\\krb5.conf");
	}

	@Test
	void sendToKafka() throws Exception {

		//SettableCompletableFuture<SendResult<String, String>> future = new SettableCompletableFuture<>();
		CompletableFuture<SendResult<Long, Map<String, Object>>> future2 = new KafkaCompletableFuture<>();	

		when(kafkaTemplate.send(Mockito.anyString(), Mockito.anyMap())).thenReturn(future2);
		activityservice.setActivityTopic("kafka");
		CompletableFuture<SendResult<Long, Map<String, Object>>> future1 = activityservice.sendToKafka(getActivityMap(), "kafka");
		assertNotNull(future1);
	}

	@Test
	void sendToKafkaWithFailOverKafkaTemplate() throws Exception {
		//SettableCompletableFuture<SendResult<String, String>> future = new SettableCompletableFuture<>();
		CompletableFuture<SendResult<Long, Map<String, Object>>> future2 = new KafkaCompletableFuture<>();
		
		when(failOverKafkaTemplate.send(Mockito.anyString(), Mockito.anyMap())).thenReturn(future2);
		activityservice.setActivityTopic("kafka");
		CompletableFuture<SendResult<Long, Map<String, Object>>> future1 = activityservice.sendToKafkaWithFailOverKafkaTemplate(getActivityMap(), "kafka");
		assertNotNull(future1);

	}

	@Test
	void sendToMQ() throws Exception {
		
		
		   ActivityService mockVoid1 = mock(ActivityService.class);
	        ArgumentCaptor<Activity> valueCapture1 = ArgumentCaptor.forClass(Activity.class);
	        doNothing().when(mockVoid1).sendToMQ(valueCapture1.capture());
	        mockVoid1.sendToMQ(getActivity());
	        assertEquals("test", valueCapture1.getValue().getAppName());
	}

	private Map<String, Object> getActivityMap() {

		Activity activity = new Activity();
		activity.setAppName("test");
		activity.setAppUser("testuser");
		activity.setDetail("details");
		activity.setEvent("event");
		activity.setEventCode("event code");
		activity.setStep("step");
		activity.setSummary("summary");
		activity.setAltbusinessIdentifier("altbusinessIdentifier");
		activity.setBusinessIdentifier("businessIdentifier");
		activity.setDomain("domain");
		activity.setEnvironment("environment");
		activity.setEventSubcode("eventSubcode");
		activity.setGlobalTransactionId("1");
		activity.setHostname("hostname");
		activity.setThreadId("2");
		activity.setServiceVersion("serviceVersion");
		activity.setServiceName("serviceName");
		activity.setReceiver("kafka");
		activity.setPayload("testpayload");
		List<NameValuePair> nameValuePairs = new ArrayList<>();
		NameValuePair nameValuePair = new NameValuePair();
		nameValuePair.setName("messagingsystem");
		nameValuePair.setValue("kafka");
		nameValuePairs.add(nameValuePair);
		activity.setNameValuePairs(nameValuePairs);
		
		
		@SuppressWarnings("unchecked")
		Map<String, Object> activityMap = new ObjectMapper().registerModule(new JodaModule()).convertValue(activity, Map.class);

		
		

		return activityMap;
	}
	
	private Activity getActivity() {

		Activity activity = new Activity();
		activity.setAppName("test");
		activity.setAppUser("testuser");
		activity.setDetail("details");
		activity.setEvent("event");
		activity.setEventCode("event code");
		activity.setStep("step");
		activity.setSummary("summary");
		activity.setAltbusinessIdentifier("altbusinessIdentifier");
		activity.setBusinessIdentifier("businessIdentifier");
		activity.setDomain("domain");
		activity.setEnvironment("environment");
		activity.setEventSubcode("eventSubcode");
		activity.setGlobalTransactionId("1");
		activity.setHostname("hostname");
		activity.setThreadId("2");
		activity.setServiceVersion("serviceVersion");
		activity.setServiceName("serviceName");
		activity.setReceiver("kafka");
		activity.setPayload("testpayload");
		List<NameValuePair> nameValuePairs = new ArrayList<>();
		NameValuePair nameValuePair = new NameValuePair();
		nameValuePair.setName("messagingsystem");
		nameValuePair.setValue("kafka");
		nameValuePairs.add(nameValuePair);
		activity.setNameValuePairs(nameValuePairs);
		
		

		return activity;
	}

}

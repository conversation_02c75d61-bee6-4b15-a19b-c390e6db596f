package com.emc.it.eis.activity.test;

import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

@com.emc.it.eis.activity.test.context.TestConfiguration
public class TestConfiguration {
	
	private final HttpSecurity mockHttpSecurity = Mockito.mock(HttpSecurity.class);
	
	private final SecurityFilterChain mockSecurityFilterChain = Mockito.mock(SecurityFilterChain.class);
	
	
	@Bean	
	@Primary
	SecurityFilterChain mockSecurityFilterChain()
	{
		return mockSecurityFilterChain;
	}
	
	@Bean	
	@Primary
	HttpSecurity mockHttpSecurity()
	{
		return mockHttpSecurity;
	}

}

package com.emc.it.eis.activity;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * Unit tests for ActmonClientUtil class, specifically testing the Kubernetes migration
 * from getCFHost to getPodHost method.
 *
 * Note: These tests use system properties to simulate environment variables since
 * mocking System.getenv() is not reliable in all test environments.
 */
class ActmonClientUtilTest {

    private Map<String, String> originalSystemProperties;

    @BeforeEach
    void setUp() {
        // Store original system properties to restore later
        originalSystemProperties = new HashMap<>();
        String[] envVars = {"MY_POD_NAMESPACE", "MY_POD_NAME", "app_config_name", "KUBERNETES_NAMESPACE", "HOSTNAME"};
        for (String envVar : envVars) {
            originalSystemProperties.put(envVar, System.getProperty(envVar));
        }

        // Clear all environment-related system properties
        for (String envVar : envVars) {
            System.clearProperty(envVar);
        }
    }

    @AfterEach
    void tearDown() {
        // Restore original system properties
        for (Map.Entry<String, String> entry : originalSystemProperties.entrySet()) {
            if (entry.getValue() != null) {
                System.setProperty(entry.getKey(), entry.getValue());
            } else {
                System.clearProperty(entry.getKey());
            }
        }
    }

    private void setEnvironmentVariable(String key, String value) {
        if (value != null) {
            System.setProperty(key, value);
        } else {
            System.clearProperty(key);
        }
    }

    @Test
    void testGetPodHost_WithAllKubernetesEnvironmentVariables() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "aics360-cit-dev");
        setEnvironmentVariable("app_config_name", "transactionRetriggerService");
        setEnvironmentVariable("MY_POD_NAME", "transaction-retrigger-service-deployment-12345-abcde");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("aics360-cit-dev_12345-abcde_dtc", result);
    }

    @Test
    void testGetPodHost_WithFallbackNamespace() {
        // Given
        setEnvironmentVariable("KUBERNETES_NAMESPACE", "fallback-namespace");
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("MY_POD_NAME", "test-app-deployment-67890-fghij");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("fallback-namespace_67890-fghij_dtc", result);
    }

    @Test
    void testGetPodHost_WithDefaultNamespace() {
        // Given
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("MY_POD_NAME", "test-app-deployment-11111-aaaaa");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("default_11111-aaaaa_dtc", result);
    }

    @Test
    void testGetPodHost_WithHostnameFallback() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("HOSTNAME", "test-hostname-22222-bbbbb");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("test-namespace_22222-bbbbb_dtc", result);
    }

    @Test
    void testGetPodHost_WithShortPodName() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("MY_POD_NAME", "short-name");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("test-namespace_short-name_dtc", result);
    }

    @Test
    void testGetPodHost_WithMissingAppName() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("MY_POD_NAME", "test-pod-33333-ccccc");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("test-namespace_33333-ccccc_dtc", result);
    }

    @Test
    void testGetPodHost_WithNoPodName() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("app_config_name", "testApp");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("test-namespace_0_dtc", result);
    }

    @Test
    void testGetPodHost_WithMinimalEnvironment() {
        // Given - no environment variables set

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("default_0_dtc", result);
    }

    @Test
    void testGetPodHost_WithComplexPodName() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "production");
        setEnvironmentVariable("app_config_name", "complexApp");
        setEnvironmentVariable("MY_POD_NAME", "complex-app-service-deployment-v2-44444-ddddd");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("production_44444-ddddd_dtc", result);
    }

    @Test
    void testGetCFHost_DeprecatedMethod() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("MY_POD_NAME", "test-pod-55555-eeeee");

        // When
        String result = ActmonClientUtil.getCFHost();

        // Then
        assertEquals("test-namespace_55555-eeeee_dtc", result);
    }

    @Test
    void testGetPodHost_WithEmptyStrings() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "");
        setEnvironmentVariable("app_config_name", "");
        setEnvironmentVariable("MY_POD_NAME", "");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        assertEquals("default_0_dtc", result);
    }

    @Test
    void testGetPodHost_WithSingleDashPodName() {
        // Given
        setEnvironmentVariable("MY_POD_NAMESPACE", "test-namespace");
        setEnvironmentVariable("app_config_name", "testApp");
        setEnvironmentVariable("MY_POD_NAME", "single-pod");

        // When
        String result = ActmonClientUtil.getPodHost();

        // Then
        // For a pod name with only one dash, the entire name is used as the instance identifier
        assertEquals("test-namespace_single-pod_dtc", result);
    }
}

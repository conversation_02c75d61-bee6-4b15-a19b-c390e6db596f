<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:int="http://www.springframework.org/schema/integration"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd">

	<context:property-placeholder ignore-unresolvable="true" />

	<bean id="cic.activity.monitor.out.gateway" class="com.emc.it.eis.activity.OutGateway" />

	<bean id="hardFailureGateway" class="com.emc.it.eis.activity.OutGateway">
		<property name="failSend" value="true" />
	</bean>

	<int:channel id="cic.activity.monitor.out" />
	
	<bean id="actmonLoggingProperties" class="com.emc.it.eis.activity.ActmonLoggingProperties">
		<property name="actmonLoggingEnabled" value="${actmon.logging.flag:true}" />
		<property name="actmonFlow" value="${actmon.flow:}" />
		<property name="actmonStartEnabled" value="${actmon.start.logging.flag:true}" />
		<property name="actmonTerminatedEnabled" value="${actmon.terminate.logging.flag:true}" />
		<property name="actmonFailedEnabled" value="${actmon.failed.logging.flag:true}" />
		<property name="actmonInfoEnabled" value="${actmon.info.logging.flag:true}" />
		<property name="actmonWarningEnabled" value="${actmon.warning.logging.flag:true}" />
		<property name="actmonCompleteEnabled" value="${actmon.complete.logging.flag:true}" />
	</bean>
	
	<bean id="rulesConfigurationProperties" class="com.emc.it.eis.activity.RulesConfigurationProperties">
		<property name="rulesConfigurationEnabled" value="${rules.configuration.flag:true}" />
		<property name="rulesStartEnabled" value="${rules.start.configuration.flag:true}" />
		<property name="rulesTerminatedEnabled" value="${rules.terminate.configuration.flag:true}" />
		<property name="rulesFailedEnabled" value="${rules.failed.configuration.flag:true}" />
		<property name="rulesInfoEnabled" value="${rules.info.configuration.flag:true}" />
		<property name="rulesWarningEnabled" value="${rules.warning.configuration.flag:true}" />
		<property name="rulesCompleteEnabled" value="${rules.complete.configuration.flag:true}" />
	</bean>

	<bean id="eventMarshaller" class="org.springframework.oxm.jaxb.Jaxb2Marshaller">
		<property name="contextPath" value="com.emc.it.enterprise.data.v1" />
	</bean>

	<bean id="cic.activity.monitor.transactionalSenderNoFlowControl" class="com.emc.it.eis.activity.ActivityInterceptor$TransactionalSenderNoFlowControlImpl">
		<constructor-arg ref="cic.activity.monitor.out.gateway" />
	</bean>

<!--
	<bean id="activityMonitorReloadableProperties" class="com.emc.it.eis.activity.ReloadablePropertiesImpl" />
-->

	<bean id="baseActivityMonitorInterceptor" class="com.emc.it.eis.activity.ActivityInterceptor" abstract="true">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg ref="cic.activity.monitor.transactionalSenderNoFlowControl" />
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
		<property name="baseHeaderExpressions">
			<util:map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" value="'${cic.process.id:UNDEFINED}'" />
			</util:map>
		</property>
		<property name="baseActivityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</util:map>
		</property>
		<property name="baseActivityAttributes">
			<util:map id="activityAttributes">
				<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }" />
				<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorBasicHeader" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorBasicActivity" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorPayloadContext" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'domain'" />
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorSuppressPayload" class="com.emc.it.eis.activity.ActivityInterceptor">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg ref="cic.activity.monitor.transactionalSenderNoFlowControl" />
<!--		<constructor-arg>
			<bean class="com.emc.it.eis.activity.ActivityInterceptorTests.SuppressingReloadablePropertiesImpl" />
		</constructor-arg>-->
		<property name="baseHeaderExpressions">
			<util:map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" value="'${cic.process.id:UNDEFINED}'" />
			</util:map>
		</property>
		<property name="baseActivityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</util:map>
		</property>
		<property name="baseActivityAttributes">
			<util:map id="activityAttributes">
				<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }" />
				<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }" />
			</util:map>
		</property>
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'domain'" />
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorJson" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorException" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorNameValue" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityExpressions">
			<util:map>
				<entry key="Payload" value="payload" />
			</util:map>
		</property>
		<property name="nameValuePairs">
			<util:map>
				<entry key="someName" value="someValue" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorHardFailure" class="com.emc.it.eis.activity.ActivityInterceptor">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg>
			<bean class="com.emc.it.eis.activity.ActivityInterceptor$TransactionalSenderNoFlowControlImpl">
				<constructor-arg ref="hardFailureGateway" />
			</bean>
		</constructor-arg>
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
		<property name="baseHeaderExpressions">
			<util:map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" value="'${cic.process.id:UNDEFINED}'" />
			</util:map>
		</property>
		<property name="baseActivityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</util:map>
		</property>
		<property name="baseActivityAttributes">
			<util:map id="activityAttributes">
				<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }" />
				<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }" />
			</util:map>
		</property>
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="failOnActivityMonitorFailure" value="true" />
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="hardFailureAndRecoveryGateway" class="com.emc.it.eis.activity.OutGateway">
		<property name="failSend" value="true" />
	</bean>

	<bean id="activityMonitorInterceptorHardFailureAndRecovery" class="com.emc.it.eis.activity.ActivityInterceptor">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg>
			<bean class="com.emc.it.eis.activity.ActivityInterceptor$TransactionalSenderNoFlowControlImpl">
				<constructor-arg ref="hardFailureAndRecoveryGateway" />
			</bean>
		</constructor-arg>
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
		<property name="baseHeaderExpressions">
			<util:map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" value="'${cic.process.id:UNDEFINED}'" />
			</util:map>
		</property>
		<property name="baseActivityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</util:map>
		</property>
		<property name="baseActivityAttributes">
			<util:map id="activityAttributes">
				<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }" />
				<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }" />
			</util:map>
		</property>
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="activityMonitorInterceptorExistingGTxAttemptOverwrite" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).GLOBAL_TRANSACTION_ID}" value="'over'" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="failureAndRecoveryFailureAndRecoveryGateway" class="com.emc.it.eis.activity.OutGateway">
		<property name="failSend" value="true" />
	</bean>

	<bean id="activityMonitorInterceptorFailureAndRecoveryFailureAndRecovery" class="com.emc.it.eis.activity.ActivityInterceptor">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg>
			<bean class="com.emc.it.eis.activity.ActivityInterceptor$TransactionalSenderNoFlowControlImpl">
				<constructor-arg ref="failureAndRecoveryFailureAndRecoveryGateway" />
			</bean>
		</constructor-arg>
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
		<property name="baseHeaderExpressions">
			<util:map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" value="'${cic.process.id:UNDEFINED}'" />
			</util:map>
		</property>
		<property name="baseActivityExpressions">
			<util:map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</util:map>
		</property>
		<property name="baseActivityAttributes">
			<util:map id="activityAttributes">
				<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }" />
				<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }" />
			</util:map>
		</property>
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="activityMonitorInterceptorChannelInNvp" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="activityAttributes">
			<util:map>
				<entry key="Step" value="my step" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="activityMonitorInterceptorExistingGTx" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>

	<bean id="activityMonitorInterceptorSpELGTx" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).GLOBAL_TRANSACTION_ID}" value="'SpEL'" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonLoggingProperties" />
	</bean>
	
	<bean id="actmonNoLoggingProperties" class="com.emc.it.eis.activity.ActmonLoggingProperties">
		<property name="actmonLoggingEnabled" value="${actmon.logging.flag:false}" />
		<property name="actmonFlow" value="${actmon.flow:}" />
		<property name="actmonStartEnabled" value="${actmon.start.logging.flag:true}" />
		<property name="actmonTerminatedEnabled" value="${actmon.terminate.logging.flag:true}" />
		<property name="actmonFailedEnabled" value="${actmon.failed.logging.flag:true}" />
		<property name="actmonInfoEnabled" value="${actmon.info.logging.flag:true}" />
		<property name="actmonWarningEnabled" value="${actmon.warning.logging.flag:true}" />
		<property name="actmonCompleteEnabled" value="${actmon.complete.logging.flag:true}" />
	</bean>
	
	<bean id="actmonNoStartLoggingProperties" class="com.emc.it.eis.activity.ActmonLoggingProperties">
		<property name="actmonLoggingEnabled" value="${actmon.logging.flag:true}" />
		<property name="actmonFlow" value="${actmon.flow:}" />
		<property name="actmonStartEnabled" value="${actmon.start.logging.flag:false}" />
		<property name="actmonTerminatedEnabled" value="${actmon.terminate.logging.flag:true}" />
		<property name="actmonFailedEnabled" value="${actmon.failed.logging.flag:true}" />
		<property name="actmonInfoEnabled" value="${actmon.info.logging.flag:true}" />
		<property name="actmonWarningEnabled" value="${actmon.warning.logging.flag:true}" />
		<property name="actmonCompleteEnabled" value="${actmon.complete.logging.flag:true}" />
	</bean>
	
	<bean id="activityMonitorInterceptorNoLogging" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonNoLoggingProperties" />
	</bean>
	
	<bean id="activityMonitorInterceptorNoStartLogging" class="com.emc.it.eis.activity.ActivityInterceptor" parent="baseActivityMonitorInterceptor">
		<property name="headerExpressions">
			<util:map>
				<entry key="test" value="headers.id" />
			</util:map>
		</property>
		<property name="actmonLoggingProperties" ref="actmonNoStartLoggingProperties" />
	</bean>
	
	<bean id="objectMapper" class="com.emc.it.eis.activity.mapper.CustomMapper" />
</beans>

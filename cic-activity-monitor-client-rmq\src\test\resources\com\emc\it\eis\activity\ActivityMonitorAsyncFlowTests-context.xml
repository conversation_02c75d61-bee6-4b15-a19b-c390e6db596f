<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
       xmlns:int="http://www.springframework.org/schema/integration"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:int-groovy="http://www.springframework.org/schema/integration/groovy"
       xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
		http://www.springframework.org/schema/integration/groovy http://www.springframework.org/schema/integration/groovy/spring-integration-groovy.xsd
		http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">


    <import resource="classpath:/META-INF/spring/cic-activity-monitor-client/activity-monitor-common-context.xml"/>


    <import resource="classpath:/META-INF/spring/cic-config/cic-common-properties-config.xml"/>

    <util:properties id="cic.application.properties">
        <prop key="application.configuration.name">flow-test-app</prop>
        <prop key="cic.process.id">flow-test-process</prop>
        <prop key="am.environment">dev</prop>
        <prop key="am.serviceName">service</prop>
    </util:properties>

    <int:channel id="inputChannel">
        <int:dispatcher task-executor="executor"/>
    </int:channel>

    <int:router input-channel="inputChannel" expression="'outputChannel'+ payload % 3"/>

    <task:executor id="executor" pool-size="10"/>

    <int-flow:outbound-gateway flow="activity-monitor-process-terminated-event" input-channel="outputChannel0"/>

    <int-flow:outbound-gateway flow="activity-monitor-process-completed-event" input-channel="outputChannel1"/>

    <int-flow:outbound-gateway flow="activity-monitor-process-failed-event" input-channel="errorChannel"/>

    <int:transformer input-channel="outputChannel2" output-channel="errorChannel">
        <int-groovy:script>
            return new org.springframework.messaging.MessagingException("message failed");
        </int-groovy:script>
    </int:transformer>

</beans>

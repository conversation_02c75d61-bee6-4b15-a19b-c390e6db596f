<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:int-stream="http://www.springframework.org/schema/integration/stream"
    xmlns:util="http://www.springframework.org/schema/util"
    xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/integration/stream http://www.springframework.org/schema/integration/stream/spring-integration-stream.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<bean id="payloadActivityMonitorInterceptor" parent="basePayloadActivityMonitorInterceptor" abstract="true">
		<property name="baseHeaderExpressions">
			<map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" 
					value="'${application.configuration.name:UNDEFINED}'" />
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" 
					value="'${cic.process.id:UNDEFINED}'" />
			</map>
		</property>
	</bean>	

	<bean id="noPayloadActivityMonitorInterceptor" parent="baseActivityMonitorInterceptor" abstract="true">
		<property name="baseHeaderExpressions">
			<map>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).APPLICATION_ID}" 
					value="'${application.configuration.name:UNDEFINED}'"/>
				<entry key="#{ T(com.emc.it.eis.common.integration.CICHeaders).PROCESS_ID}" 
					value="'${cic.process.id:UNDEFINED}'"/>
			</map>
		</property>
	</bean>	

	<int:channel id="inbound">
		<int:interceptors>
			<bean parent="payloadActivityMonitorInterceptor">
				<property name="activityAttributes">
					<map>
						<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).PROCESS.toString() }"/>
						<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).STARTED.toString() }"/>
					</map>
				</property>
				<!-- synthesize a payload context because we don't have a real one yet -->
				<property name="activityExpressions">
					<map>
						<entry key="PayloadContext.MessageProfile.Domain" value="'MY_DOMAIN'"/>
						<entry key="PayloadContext.MessageProfile.Process" 
							value="'${cic.process.id:UNDEFINED}'"/>
						<entry key="PayloadContext.ApplicationProfile.AppName" 
							value="'${application.configuration.name:UNDEFINED}'"/>
						<entry key="PayloadContext.TransactionProfile.GlobalTransactionID" value="headers['#{ T(com.emc.it.eis.common.integration.CICHeaders).GLOBAL_TRANSACTION_ID}']"/>						
					</map>
				</property>
			</bean>
		</int:interceptors>
	</int:channel>
	
	<int:channel id="warning">
		<int:interceptors>
			<bean parent="noPayloadActivityMonitorInterceptor"> <!-- no payload -->
				<property name="activityAttributes">
					<map>
						<entry key="Event" value="#{ T(com.emc.it.eis.activity.Event).LOG.toString() }"/>
						<entry key="Status" value="#{ T(com.emc.it.eis.activity.Status).WARNING.toString() }"/>
						<entry key="Detail" value="Some warning"/>
					</map>
				</property>
				<!-- This is an example where we assume there is already a Payload Context so we don't have to synthesize one -->
			</bean>
		</int:interceptors>
	</int:channel>
	
	<int-stream:stdout-channel-adapter channel="inbound" append-newline="true"/>
	
	<int-stream:stdout-channel-adapter channel="warning" append-newline="true"/>

	<beans profile="dev">
	    <util:properties id="cic.application.properties">
	        <prop key="application.configuration.name">activity-monitor-test-client</prop>
	        <prop key="cic.process.id">activity-monitor-test-process</prop>
	    </util:properties>
	</beans>	
</beans>

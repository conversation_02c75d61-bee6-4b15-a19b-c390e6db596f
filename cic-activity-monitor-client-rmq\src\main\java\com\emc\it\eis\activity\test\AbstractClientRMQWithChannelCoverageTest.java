package com.emc.it.eis.activity.test;

import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.test.context.TestPropertySource;

import com.emc.it.eis.activity.RulesConfigurationProperties;
import com.emc.it.eis.activity.config.ActivityConfig;
import com.emc.it.eis.activity.config.RabbitMQConfig;
import com.emc.it.eis.activity.config.util.ConfigUtil;
import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
import com.emc.it.eis.common.integration.test.coverage.AbstractPackageChannelCoverageTests;

@TestPropertySource(locations = {"classpath:cic-activity-monitor-client.properties"},properties = {"app_config_name=test-app-config",
		"spring.main.allow-bean-definition-overriding=true", "spring.main.allow-circular-references=true", "spring.cloud.config.enabled=false", "ssl-truststore-location=test",
		"dedup.config.service.url=http://localhost:12345",
		"dedup.config.service.userName=test", "dedup.config.service.password=test", "spring.application.name=testapp", "aic.oauth2.enabled=false", "lookup.table.client.cache.load=false", "aic.oauth2.enabled.resttemplate=false", "payload.size.threshold=0"})
@ImportAutoConfiguration({RefreshAutoConfiguration.class, FeignAutoConfiguration.class})
@Import({TestConfiguration.class})
public abstract class AbstractClientRMQWithChannelCoverageTest extends AbstractPackageChannelCoverageTests {
	
	
	@MockBean
	private RabbitMQConfig rabbitMQConfig;	
	
	@MockBean
	private RulesConfigurationProperties rulesConfigurationProperties;

	@MockBean
	private ActivityConfig activityConfig;	

	/*
	 * @MockBean private FeignConfiguration feignConfiguration;
	 */
	
	@MockBean
	private ActivityService activityService;

	@MockBean
	private AsyncHelper asyncHelper;
	
	@MockBean
	private ConfigUtil configUtil;	


	@MockBean
	@Qualifier("commonBtmAmqpConnectionFactory")
	private ConnectionFactory connectionFactory;
	
	@MockBean
	ClientRegistrationRepository clientRegistrationRepository;
	
}

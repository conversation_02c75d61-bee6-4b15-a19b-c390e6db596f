<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="clientPropertyBean" class="com.emc.it.eis.activity.restclient.RestClientProperties">
		<constructor-arg index="0" name="host" value="${cic.actmon.host:localhost}" />
		<constructor-arg index="1" name="port" value="${cic.actmon.port:8080}" />
		<constructor-arg index="2" name="contextPath"
			value="${cic.actmon.context.path:cic-activity-monitor-webservice/actmon.ws}" />
		<constructor-arg index="3" name="httpType" value="${cic.actmon.http.type:http}" />

		<property name="wsAvailable" value="${cic.actmon.ws.available:true}" />
		<property name="apiKey" value="${cic.actmon.ws.apikey:}" />
	</bean>

	<bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
		<property name="requestFactory" ref="httpRestClientFactory" />
	</bean>

	<bean id="httpRestClientFactory" class="org.springframework.http.client.HttpComponentsClientHttpRequestFactory">
		<constructor-arg ref="httpRestClient" />
	</bean>

	<bean id="httpClientBuilder" class="org.apache.hc.client5.http.impl.classic.HttpClientBuilder" factory-method="create" primary="true">
		<property name="connectionManager">
			<bean class="org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager" />
		</property>
		<property name="defaultRequestConfig" ref="requestConfig" />
	</bean>

	<bean id="basicCredentialsProvider" class="org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider" />

	<bean id="httpRestClient" factory-bean="httpClientBuilder" factory-method="build" />

	<bean id="requestConfigBuilder" class="org.apache.hc.client5.http.config.RequestConfig" factory-method="custom">		
		<property name="connectTimeout" ref="connectTimeout" />
	</bean>
	
	<bean id="connectTimeout" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetClass" value="org.apache.hc.core5.util.Timeout" />
		<property name="targetMethod" value="parse" />		
		<property name="arguments">
			<list>
				<value>${activity.monitor.ws.conn.timeout:30 SECONDS}</value>
			</list>
		</property>
	</bean>

	<bean id="requestConfig" factory-bean="requestConfigBuilder" factory-method="build" />

	<!-- <bean id="restCredentials" class="org.apache.http.auth.UsernamePasswordCredentials">
		<constructor-arg value="${cic.rest.webservice.username}" />
		<constructor-arg value="${cic.rest.webservice.password}" />
	</bean> -->

	<bean id="actmonRestClient" class="com.emc.it.eis.activity.restclient.ActmonRestClient">
		<constructor-arg name="restTemplate" ref="restTemplate" />
	</bean>

</beans>

package com.emc.it.eis.activity.restclient;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.oauth2client.OAuthClientCredentialsRestTemplateInterceptor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Configuration
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "actmon.aic.oauth2.enabled.resttemplate", havingValue = "true")
public class Outh2RestTemplateConfiguration {
	
	public static final String ACTMON_CLIENT_OAUTH2 = "ACTMON_CLIENT_OAUTH2";
	public static final String ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER = "ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER";
	
	private final RestTemplateBuilder restTemplateBuilder;
	
	private final ClientRegistrationRepository clientRegistrationRepository;	
	
	@Autowired
	@Qualifier(ACTMON_CLIENT_AUTOHORIZED_CLIENT_MANAGER)
	OAuth2AuthorizedClientManager authorizedClientManager;
	

	@Bean(ACTMON_CLIENT_OAUTH2)
	@ConditionalOnProperty(name = "actmon.aic.oauth2.enabled.resttemplate", havingValue = "true")
	RestTemplate restTemplate() {
		ClientRegistration clientRegistration = clientRegistrationRepository.findByRegistrationId("actmon-client-dias-creds");
		log.info("Registered ClientRegistrationRepository for client-rmq-dias-creds");
		return restTemplateBuilder
				.additionalInterceptors(new OAuthClientCredentialsRestTemplateInterceptor(authorizedClientManager, clientRegistration))
				.readTimeout(Duration.ofSeconds(5)).connectTimeout(Duration.ofSeconds(1))
				.build();
	} 

}

/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.activity;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.joda.time.DateTime;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.http.ResponseEntity;
import org.springframework.integration.context.IntegrationObjectSupport;
import org.springframework.integration.handler.ExpressionEvaluatingMessageProcessor;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.xml.transform.StringResult;
import org.springframework.xml.transform.StringSource;

import com.emc.it.eis.activity.domain.Activity;
import com.emc.it.eis.activity.domain.RMQPayload;
import com.emc.it.eis.activity.domain.Result;
import com.emc.it.eis.activity.exception.RequestValidationException;
import com.emc.it.eis.activity.service.ActivityService;
import com.emc.it.eis.activity.service.AsyncHelper;
import com.emc.it.eis.common.integration.CICHeaders;
import com.emc.it.eis.common.xml.SimpleContentExtractor;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.emc.it.enterprise.data.v1.DateTimeType;
import com.emc.it.enterprise.data.v1.TextType;
import com.emc.it.enterprise.msg.v1.ApplicationProfileType;
import com.emc.it.enterprise.msg.v1.MessageProfileType;
import com.emc.it.enterprise.msg.v1.PayloadContext;
import com.emc.it.enterprise.msg.v1.TransactionProfileType;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.pivotal.cfenv.core.CfApplication;
import io.pivotal.cfenv.core.CfEnv;
import lombok.extern.slf4j.Slf4j;

/**
 * Channel interceptor that sends an event to the activity monitoring service
 * using Rabbit Q. Optionally adds headers to the message (enrichment) first;
 * these headers are retained and continue with the message. This facility might
 * be used to add, say, a global transaction id to a message at the beginning of
 * the flow, at the same time as sending a PROCESS|Started event.
 *
 * <AUTHOR> Russell
 */
@Slf4j
public class ActivityInterceptor implements ChannelInterceptor, BeanFactoryAware, InitializingBean {

	private Map<String, ExpressionEvaluatingMessageProcessor<String>> headerProcessors = new HashMap<>();

	private Map<String, ExpressionEvaluatingMessageProcessor<String>> activityProcessors = new HashMap<>();

	private final Map<String, String> activityAttributes = new HashMap<>();

	private final Map<String, String> nameValuePairs = new HashMap<>();

	protected volatile boolean failOnActivityMonitorFailure;

	protected Jaxb2Marshaller eventMarshaller;

	private final SimpleContentExtractor payloadContextExtractor = new SimpleContentExtractor("//PayloadContext");

	private static Log logger = LogFactory.getLog(ActivityInterceptor.class);

	private static final Pattern valueAttributePattern = Pattern.compile("[^.]+\\.Value", Pattern.CASE_INSENSITIVE);

	private static final Pattern decapitalizePattern = Pattern.compile("\\.[A-Z]");

	protected final AtomicInteger failures = new AtomicInteger();

	private final String hostName;

	@Value("${kafka.flow.enabled}")
	private String kafkaFlowEnabled;
	
	@Value("${segment.flow.check.enabled:false}")
	private String segmentFlowCheckEnabled;

	// protected ReloadableProperties reloadableProperties;
	private TransactionalSenderNoFlowControl transactionalSenderNoFlowControl;

	private CfEnv environment = new CfEnv();

	/**
	 * {@code BeanFactory} to inject into {@link #headerProcessors} and
	 * {@link #activityProcessors}
	 */
	private BeanFactory beanFactory;

	private ActmonLoggingProperties actmonLoggingProperties;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private AsyncHelper asyncHelper;
	
	@Autowired
	private ObjectMapper objectMapper;

	public ObjectMapper getObjectMapper() {
		return objectMapper;
	}

	public void setObjectMapper(ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}


	@Value("${kafka.payload.max.size.bytes}")
	private String KAFKA_PAYLOAD_MAX_SIZE_BYTES;

	public ActmonLoggingProperties getActmonLoggingProperties() {
		return actmonLoggingProperties;
	}

	public void setActmonLoggingProperties(ActmonLoggingProperties actmonLoggingProperties) {
		this.actmonLoggingProperties = actmonLoggingProperties;
	}

	public ActivityInterceptor() {
		String hostName = "";
		try {

			CfApplication cfApplication = this.environment.getApp();
			logger.info("checking for PCF VCAP_APPLICATION...");
			if (!ObjectUtils.isEmpty(cfApplication)) {
				logger.info("Get details from VCAP_APPLICATION on PCF");
				hostName = ActmonClientUtil.getCFHost(cfApplication);
			} else {
				logger.info("Get details from host name from TC");
				InetAddress localhost = InetAddress.getLocalHost();
				hostName = localhost.getHostName();
			}
		} catch (UnknownHostException e) {
			logger.error("Failed to get hostname", e);
			hostName = "unknown";
		}
		catch (Exception e) {
			logger.error("got exception in ActivityInterceptor setting hostname " + e.getMessage());
		}
		this.hostName = hostName;
		logger.info("ActivityInterceptor setting hostname to " + this.hostName);
	}

	public ActivityInterceptor(Jaxb2Marshaller eventMarshaller, TransactionalSenderNoFlowControl sender) {
		this(eventMarshaller, sender, null);
	}

	public ActivityInterceptor(Jaxb2Marshaller eventMarshaller, TransactionalSenderNoFlowControl sender,
			Map<String, String> activityExpressions) {
		this.eventMarshaller = eventMarshaller;
		this.transactionalSenderNoFlowControl = sender;
		// this.reloadableProperties = reloadableProperties;
		if (activityExpressions != null) {
			this.setActivityExpressions(activityExpressions);
		}
		String hostName = "";
		try {
			CfApplication cfApplication = this.environment.getApp();
			logger.info("checking for PCF VCAP_APPLICATION...");
			if (!ObjectUtils.isEmpty(cfApplication)) {
				logger.info("Get details from VCAP_APPLICATION on PCF");
				hostName = ActmonClientUtil.getCFHost(cfApplication);
			} else {
				logger.info("Get details from host name from TC");
				InetAddress localhost = InetAddress.getLocalHost();
				hostName = localhost.getHostName();
			}
		} catch (UnknownHostException e) {
			logger.error("Failed to get hostname", e);
			hostName = "unknown";
		}
		catch (Exception e) {
			logger.error("got exception in ActivityInterceptor setting hostname " + e.getMessage());
		}		
		this.hostName = hostName;
		logger.info("ActivityInterceptor setting hostname to " + this.hostName);
	}

	/**
	 * Common method used in both classes <code>ActivityInterceptor.java</code> and
	 * <code>ActivityInterceptorREST.java</code>. This method extracts all required
	 * data from <code>Message</code> and returns <code>Message</code> so caller
	 * method can continue with message sending flow.
	 *
	 * @param message
	 * @param channel
	 * @param createEventRequest
	 * @return Message<?>
	 */
	protected Message<?> doCommon(Message<?> message, MessageChannel channel, CreateEventRequest createEventRequest) {
		try {
			BeanWrapper beanWrapper = getBeanWrapper(createEventRequest);

			/*
			 * For ErrorMessages, get the original failed message.
			 */
			if (message instanceof ErrorMessage) {
				MessagingException messagingException = (MessagingException) message.getPayload();
				beanWrapper.setPropertyValue("Document.EventActivity.Detail.Value", getStackTrace(messagingException));
				Message<?> failedMessage = messagingException.getFailedMessage();
				if (failedMessage != null) {
					message = failedMessage;
				} else {
					logger.debug("No failedMessage on ErrorMessage: " + message);
				}
			}

			Map<String, Object> headers = new HashMap<>();
			// GTxId first priority - already in the headers.
			String globalTxId = (String) message.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID);

			globalTxId = this.enrichHeaders(message, headers, globalTxId);

			Object payloadObject = message.getPayload();
			PayloadContext payloadContext = null;
			if (payloadObject instanceof String) {
				payloadContext = findPayloadContextInPayload(payloadObject);
			} else {
				if (payloadObject instanceof List list && !list.isEmpty()) {
					payloadContext = findPayloadContextInPayload(list.get(0));
				}
				payloadObject = objectToJson(payloadObject);
			}

			String pcTxId = null;
			if (payloadContext != null) {
				createEventRequest.setPayloadContext(payloadContext);
				// third priority for GTxId is from Payload Context
				pcTxId = (String) beanWrapper.getPropertyValue("PayloadContext.TransactionProfile.GlobalTransactionID");
				if (globalTxId == null) {
					globalTxId = pcTxId;
				} else {
					if (pcTxId != null && !globalTxId.equals(pcTxId)) {
						logger.error("Global Tx ID in Header (" + globalTxId + ") is different to "
								+ "payload context (" + pcTxId + ")");
					}
				}
			}

			// if we still don't have a GTxId; use the message id
			if (globalTxId == null) {
				globalTxId = message.getHeaders().getId().toString();				
			}

			if (pcTxId == null) {
				beanWrapper.setPropertyValue("PayloadContext.TransactionProfile.GlobalTransactionID", globalTxId);
			}

			if (message.getHeaders().get(CICHeaders.GLOBAL_TRANSACTION_ID) == null) {
				headers.put(CICHeaders.GLOBAL_TRANSACTION_ID, globalTxId);
			}

			if (headers.size() > 0) {
				message = MessageBuilder.fromMessage(message).copyHeaders(headers).build();
			}

			Message<?> messageToSend = message;
			if (payloadObject != message.getPayload()) { // json
				messageToSend = MessageBuilder.withPayload(payloadObject).copyHeaders(message.getHeaders()).build();
			}

			this.setEventAttributes(beanWrapper, messageToSend);

			this.addHeaders(message, beanWrapper);

			/*
			 * Set default attributes if not already set...
			 */
			if (beanWrapper.getPropertyValue("PayloadContext.TransactionProfile.transactionDateTime") == null) {
				DateTimeType now = new DateTimeType();
				now.setValue(new DateTime());
				createEventRequest.getPayloadContext().getTransactionProfile().setTransactionDateTime(now);
			}

			this.setHostThreadChannel(channel, beanWrapper);

		} catch (Exception e) {
			logger.error("Failed to prepare activity event: " + message, e);
			if (this.failOnActivityMonitorFailure) {
				throw new MessagingException(message, e);
			}
			return message;
		}
		/*
		 * Validate the request
		 */
		try {
			CreateEventRequestValidator.validate(createEventRequest);
		} catch (RequestValidationException e) {
			logger.error("Failed to validate activity event: " + objectToJson(createEventRequest), e);
			if (this.failOnActivityMonitorFailure) {
				throw new MessagingException(message, e);
			}
			return message;
		}

		return message;
	}

	@Override
	public Message<?> preSend(Message<?> message, MessageChannel channel) {
		CreateEventRequest createEventRequest = new CreateEventRequest();
		message = doCommon(message, channel, createEventRequest);
		String actmonFlow = getActmonFlow();
		log.info("app flow is coming as {}", actmonFlow);
		
		if ( Constants.KAFKA_FLOW.equalsIgnoreCase(actmonFlow)) {
			/*
			 * Send the document.
			 */
			try {
				doKafkaSend(createEventRequest);
				if (this.failures.get() > 0) {
					recordKafkaFailures(createEventRequest, channel);
				}
			} catch (Exception e) {
				this.failures.incrementAndGet();
				logger.error("Failed to store activity event: " + objectToJson(createEventRequest), e);
				if (this.failOnActivityMonitorFailure) {
					throw new MessagingException("Failure while sending message to actmon", e);
				}
			}			
		}
		else
		{		
		  // Sending the message in ASYNC way
		  CompletableFuture.runAsync(() -> asyncSend(channel, createEventRequest));
		}
		return message;
	}

	public void asyncSend(MessageChannel channel, CreateEventRequest createEventRequest) {
		/*
		 * Send the document.
		 */
		try {
			doSend(createEventRequest);
			if (this.failures.get() > 0) {
				recordFailures(createEventRequest, channel);
			}
		} catch (Exception e) {
			this.failures.incrementAndGet();
			logger.error("Failed to store activity event: " + objectToJson(createEventRequest), e);
			if (this.failOnActivityMonitorFailure) {
				throw new MessagingException("Failure while sending message to actmon", e);
			}
		}
	}

	/**
	 * @param message
	 * @param beanWrapper
	 */
	private void addHeaders(Message<?> message, BeanWrapper beanWrapper) {
		/*
		 * Add headers and any additional name/value pairs.
		 */
		@SuppressWarnings("unchecked")
		List<NameValuePair> nameValuePairList = (List<NameValuePair>) beanWrapper
				.getPropertyValue("Document.EventActivity.NameValuePairs.NameValuePairs");
		for (String header : message.getHeaders().keySet()) {
			NameValuePair nvp = new NameValuePair();
			nvp.setName(header);
			nvp.setValue(message.getHeaders().get(header).toString());
			nameValuePairList.add(nvp);
		}
		for (String name : this.nameValuePairs.keySet()) {
			NameValuePair nvp = new NameValuePair();
			nvp.setName(name);
			nvp.setValue(this.nameValuePairs.get(name));
			nameValuePairList.add(nvp);
		}
	}

	/**
	 * @param message
	 * @param headers
	 * @param globalTxId
	 * @return
	 */
	private String enrichHeaders(Message<?> message, Map<String, Object> headers, String globalTxId) {
		/*
		 * Enrich the headers; second priority for GTxId is explicit SpEL expression
		 */
		for (String header : headerProcessors.keySet()) {
			if (StringUtils.isNotBlank(header)) {
				String headerValue = headerProcessors.get(header).processMessage(message);
				if (CICHeaders.GLOBAL_TRANSACTION_ID.equals(header)) {
					if (globalTxId == null) {
						globalTxId = headerValue;
					} else {
						logger.error("Message already has Global Tx ID header (" + globalTxId + ") attempt to set to ("
								+ headerValue + ") ignored");
						continue;
					}
				}
				headers.put(header, headerValue);
			}

		}
		return globalTxId;
	}

	/**
	 * @param beanWrapper
	 * @param messageToSend
	 */
	private void setEventAttributes(BeanWrapper beanWrapper, Message<?> messageToSend) {
		/*
		 * Set event attributes (literals and SpELs)
		 */
		for (String activityComponent : this.activityAttributes.keySet()) {
			setActivityAttribute(beanWrapper, activityComponent, this.activityAttributes.get(activityComponent));
		}

		for (String activityComponent : this.activityProcessors.keySet()) {
			setActivityAttribute(beanWrapper, activityComponent,
					this.activityProcessors.get(activityComponent).processMessage(messageToSend));
		}
	}

	private void setActivityAttribute(BeanWrapper beanWrapper, String activityComponent, String attributeValue) {
		// if (activityComponent.endsWith("payload")
		// &&
		// this.reloadableProperties.getActivityMonitorSuppressPayload().equalsIgnoreCase("true"))
		// {
		//if (activityComponent.endsWith("payload")) {
			//logger.info("Payload suppressed in activity event");
		//} else {
			beanWrapper.setPropertyValue(activityComponent, attributeValue);
		//}
	}

	private void setHostThreadChannel(MessageChannel channel, BeanWrapper beanWrapper) {
		beanWrapper.setPropertyValue("Document.EventActivity.HostName", this.hostName);
		beanWrapper.setPropertyValue("Document.EventActivity.ThreadID", Thread.currentThread().getName());

		@SuppressWarnings("unchecked")
		List<NameValuePair> nameValuePairList = (List<NameValuePair>) beanWrapper
				.getPropertyValue("Document.EventActivity.NameValuePairs.NameValuePairs");
		String channelName = getChannelName(channel);
		if (beanWrapper.getPropertyValue("Document.EventActivity.Step") == null) {
			if (channelName != null) {
				beanWrapper.setPropertyValue("Document.EventActivity.Step.Value", channelName);
			}
		} else {
			if (channelName != null) {
				NameValuePair nvp = new NameValuePair();
				nvp.setName("channel");
				nvp.setValue(channelName);
				nameValuePairList.add(nvp);
			}
		}
	}

	/**
	 * Use a bean wrapper to populate the event request; will create subordinate
	 * objects.
	 *
	 * @param createEventRequest
	 * @return the BeanWrapper
	 */
	private BeanWrapper getBeanWrapper(CreateEventRequest createEventRequest) {
		BeanWrapper beanWrapper = new BeanWrapperImpl(createEventRequest);
		beanWrapper.setAutoGrowNestedPaths(true);
		return beanWrapper;
	}

	/**
	 * @param createEventRequest
	 */	
	private void doSend(CreateEventRequest createEventRequest) {
		javax.xml.transform.Result result = new StringResult();
		this.eventMarshaller.marshal(createEventRequest, result);

		if (actmonLoggingProperties.getActmonLoggingEnabled()) {
			if (actmonLoggingProperties.checkActmonLogginEnabled(createEventRequest)) {

				log.info("ActivityInterceptor::doSend kafka flow enabled:{} Sending message to rabbit", kafkaFlowEnabled);

				this.transactionalSenderNoFlowControl.sendActivity(result.toString());
			}
			else {
				logger.debug("Actmon is disabled for the component: "
						+ createEventRequest.getPayloadContext().getMessageProfile().getServiceName() + " for status="
						+ createEventRequest.getDocument().getEventActivity().getStatus().getValue());
			}
		} else {
			logger.debug("Actmon is disabled for the component: "
					+ createEventRequest.getPayloadContext().getMessageProfile().getServiceName());
		}
	}

	/**
	 * @param createEventRequest
	 */
	private void doKafkaSend(CreateEventRequest createEventRequest) {
		if (actmonLoggingProperties.getActmonLoggingEnabled()) {
			if (actmonLoggingProperties.checkActmonLogginEnabled(createEventRequest)) {

				log.info("ActivityInterceptor::doSend kafka flow enabled:{} Sending message to kafka", kafkaFlowEnabled);

				try {
					long kafkaPayloadMaxSizeBytes = Long.parseLong(KAFKA_PAYLOAD_MAX_SIZE_BYTES);
					Activity activity = getActivity(createEventRequest);
					long payloadSize = ActmonClientUtil.getSize(activity.getPayload());
					if (log.isInfoEnabled()) {

						log.info("appName={} business_identifier={} payload size = {}", activity.getAppName(), activity.getBusinessIdentifier(), payloadSize);
					}
					
					DeferredResult<ResponseEntity<Result>> deferredResult = new DeferredResult<>();
				
					String topicName = null;

					if (payloadSize <= kafkaPayloadMaxSizeBytes) {
						// post msg to kafka
						log.info("Sending message to kafka");

						asyncHelper.processKafkaActivity(activity, deferredResult, topicName);

					} else {
						// payload processing logic here
						log.info("Sending payload to rabbit");
						
						RMQPayload rmqPayload = new RMQPayload();
						
						activityService.processPayload(activity, rmqPayload);
					
						log.info("Sending message to kafka");

						// replacing payload with guid
						activity.setPayload(rmqPayload.getGuid());

						asyncHelper.processKafkaActivity(activity, deferredResult, topicName);
					}

				} catch (Throwable t) {
					// TODO Auto-generated catch block
					log.error("excepiton inside ActivityInterceptor::doSend" + " kafka flow enabled:{}", kafkaFlowEnabled, t);
				}
			} else {
				logger.debug("Actmon is disabled for the component: "
						+ createEventRequest.getPayloadContext().getMessageProfile().getServiceName() + " for status="
						+ createEventRequest.getDocument().getEventActivity().getStatus().getValue());
			}
		} else {
			logger.debug("Actmon is disabled for the component: "
					+ createEventRequest.getPayloadContext().getMessageProfile().getServiceName());
		}
	}

	protected synchronized void recordFailures(CreateEventRequest createEventRequest, MessageChannel channel) {
		int howMany;
		if ((howMany = this.failures.get()) > 0) {
			this.failures.set(0);
			CreateEventRequest recordFailuresEventRequest = new CreateEventRequest();
			BeanWrapper beanWrapper = getBeanWrapper(recordFailuresEventRequest);
			beanWrapper.setPropertyValue("PayloadContext", createEventRequest.getPayloadContext());
			beanWrapper.setPropertyValue("Document.EventActivity.Event", Event.LOG.toString());
			beanWrapper.setPropertyValue("Document.EventActivity.Status.Value", Status.WARNING.toString());
			beanWrapper.setPropertyValue("Document.EventActivity.Detail.Value", howMany + " event(s) missed");
			setHostThreadChannel(channel, beanWrapper);
			try {
				doSend(recordFailuresEventRequest);
			} catch (Exception e) {
				this.failures.addAndGet(howMany + 1);
				logger.error("Failed to store failure recovery event: " + objectToJson(recordFailuresEventRequest), e);
			}
		}
	}
	
	protected synchronized void recordKafkaFailures(CreateEventRequest createEventRequest, MessageChannel channel) {
		int howMany;
		if ((howMany = this.failures.get()) > 0) {
			this.failures.set(0);
			CreateEventRequest recordFailuresEventRequest = new CreateEventRequest();
			BeanWrapper beanWrapper = getBeanWrapper(recordFailuresEventRequest);
			beanWrapper.setPropertyValue("PayloadContext", createEventRequest.getPayloadContext());
			beanWrapper.setPropertyValue("Document.EventActivity.Event", Event.LOG.toString());
			beanWrapper.setPropertyValue("Document.EventActivity.Status.Value", Status.WARNING.toString());
			beanWrapper.setPropertyValue("Document.EventActivity.Detail.Value", howMany + " event(s) missed");
			setHostThreadChannel(channel, beanWrapper);
			try {
				doKafkaSend(recordFailuresEventRequest);
			} catch (Exception e) {
				this.failures.addAndGet(howMany + 1);
				logger.error("Failed to store failure recovery event: " + objectToJson(recordFailuresEventRequest), e);
			}
		}
	}

	private String getChannelName(MessageChannel channel) {
		if (channel instanceof IntegrationObjectSupport support) {
			return support.getComponentName();
		}
		return null;
	}

	private Object getStackTrace(MessagingException messagingException) {
		StringWriter stringWriter = new StringWriter();
		PrintWriter writer = new PrintWriter(stringWriter, true);
		messagingException.printStackTrace(writer);
		return stringWriter.toString();
	}

	/**
	 * @param object
	 * @return
	 */
	protected String objectToJson(Object object) {
		try {
			StringWriter writer = new StringWriter();
			getObjectMapper().writeValue(writer, object);
			return writer.toString();
		} catch (Exception e) {
			logger.error("Could not transform object to json(" + object + "):", e);
			return "Could not transform non-string object to json";
		}
	}

	/**
	 * @param payloadObject
	 * @return
	 */
	private PayloadContext findPayloadContextInPayload(Object payloadObject) {
		if (payloadObject instanceof String payload) {
			String payloadContextString = null;
			PayloadContext payloadContext = null;
			try {
				payloadContextString = this.payloadContextExtractor.extractElement(payload);
				if (payloadContextString != null) {
					payloadContext = getPayloadContext(payloadContextString);
				}
			} catch (Exception e) {
			}
			return payloadContext;
		}
		return null;
	}

	/**
	 * For convenience, the EventActivity object is considered the root if there's
	 * no dotted component (or just 'component.Value'). Since the beanwrapper is
	 * working at the CreateEventRequest level, we need to extend the property name.
	 * Also, if a property is an instance of {@link TextType} , we add '.value' to
	 * the property name. We lower case the first char of each property name, to
	 * make it easier to check attributes later, for example: when suppressing the
	 * payload.
	 *
	 * @param activityComponent
	 * @return the extended decapilatalized name.
	 */
	private String extendNameIfNecessary(BeanWrapper beanWrapper, String activityComponent) {
		String nestedActivityComponent = activityComponent;
		if (activityComponent.indexOf(".") < 0 || valueAttributePattern.matcher(activityComponent).matches()) {
			nestedActivityComponent = "document.eventActivity." + activityComponent;
		}
		if (TextType.class.isAssignableFrom(beanWrapper.getPropertyType(nestedActivityComponent))) {
			nestedActivityComponent += ".value";
		}
		StringBuilder sb = new StringBuilder(
				nestedActivityComponent.substring(0, 1).toLowerCase() + nestedActivityComponent.substring(1));
		Matcher mat = decapitalizePattern.matcher(nestedActivityComponent);
		while (mat.find()) {
			sb.replace(mat.start(), mat.end(), mat.group().toLowerCase());
		}
		return sb.toString();
	}

	/**
	 * @param payloadContext
	 * @return
	 */
	private PayloadContext getPayloadContext(String payloadContext) {
		return (PayloadContext) this.eventMarshaller.unmarshal(new StringSource(payloadContext));
	}

	private Activity getActivity(CreateEventRequest ceRequest) throws ParseException {
		Activity activity = new Activity();

		if (ceRequest.getDocument().getEventActivity() != null) {

			if (log.isInfoEnabled()) {


				log.info("Detail is {}", ceRequest.getDocument().getEventActivity().getDetail());
			}
			if (!ObjectUtils.isEmpty(ceRequest.getDocument().getEventActivity().getDetail())) {
				activity.setDetail(ceRequest.getDocument().getEventActivity().getDetail().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Event is {}", ceRequest.getDocument().getEventActivity().getEvent());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEvent())) {
				activity.setEvent(ceRequest.getDocument().getEventActivity().getEvent());
			}

			if (log.isInfoEnabled()) {


				log.info("Event Code is {}", ceRequest.getDocument().getEventActivity().getEventCode());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEventCode())) {
				activity.setEventCode(ceRequest.getDocument().getEventActivity().getEventCode());
			}

			if (log.isInfoEnabled()) {


				log.info("Event Sub Code is {}", ceRequest.getDocument().getEventActivity().getEventSubCode());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getEventSubCode())) {
				activity.setEventSubcode(ceRequest.getDocument().getEventActivity().getEventSubCode());
			}

			if (log.isInfoEnabled()) {


				log.info("Thread ID is {}", ceRequest.getDocument().getEventActivity().getThreadID());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getThreadID())) {
				activity.setThreadId(ceRequest.getDocument().getEventActivity().getThreadID());
			}

			if (log.isInfoEnabled()) {


				log.info("Alternate Business Identifier is {}",
						ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier())) {

				activity.setAltbusinessIdentifier(
						ceRequest.getDocument().getEventActivity().getAlternateBusinessIdentifier());
			}

			if (log.isInfoEnabled()) {


				log.info("Business Identifier is {}", ceRequest.getDocument().getEventActivity().getBusinessIdentifier());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getBusinessIdentifier())) {
				activity.setBusinessIdentifier(ceRequest.getDocument().getEventActivity().getBusinessIdentifier());
			}

			log.info("Payload is {}");
			if (!ObjectUtils
					.isEmpty(ceRequest.getDocument().getEventActivity().getPayload())) {
				activity.setPayload(ceRequest.getDocument().getEventActivity().getPayload().toString());
			}
			else
			{
				log.info("Payload is coming empty{}");
			}

			if (log.isInfoEnabled()) {


				log.info("Status is {}", ceRequest.getDocument().getEventActivity().getStatus().getValue());
			}
			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getStatus().getValue())) {
				activity.setStatus(ceRequest.getDocument().getEventActivity().getStatus().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Step is {}", ceRequest.getDocument().getEventActivity().getStep());
			}

			if (!ObjectUtils.isEmpty(ceRequest.getDocument().getEventActivity().getStep())) {
				activity.setStep(ceRequest.getDocument().getEventActivity().getStep().getValue());
			}

			if (log.isInfoEnabled()) {


				log.info("Host Name is {}", ceRequest.getDocument().getEventActivity().getHostName());
			}

			if (!StringUtils.isEmpty(ceRequest.getDocument().getEventActivity().getHostName())) {
				activity.setHostname(ceRequest.getDocument().getEventActivity().getHostName());
			}

			if (log.isInfoEnabled()) {


				log.info("Name Value List is {}", ceRequest.getDocument().getEventActivity().getNameValuePairs());
			}

			if (!ObjectUtils
					.isEmpty(ceRequest.getDocument().getEventActivity().getNameValuePairs())) {

				List<NameValuePair> nameValueList = ceRequest.getDocument().getEventActivity().getNameValuePairs()
						.getNameValuePairs().stream().map(nameValue -> {

							NameValuePair nameValuePair = new NameValuePair();
							nameValuePair.setName(nameValue.getName());
							nameValuePair.setValue(nameValue.getValue());
							return nameValuePair;

						}).collect(Collectors.toList());

				activity.setNameValuePairs(nameValueList);

			}

		}

		PayloadContext payloadContext = ceRequest.getPayloadContext();

		if (payloadContext != null) {
			log.info("payloadContext is {}", payloadContext);

			MessageProfileType messageProfile = payloadContext.getMessageProfile();

			if (messageProfile != null) {

				if (messageProfile.getDomain() != null) {

					if (log.isInfoEnabled()) {


						log.info("Domain is {}", messageProfile.getDomain());
					}
					activity.setDomain(messageProfile.getDomain());
				}
			}

			ApplicationProfileType applicationProfile = payloadContext.getApplicationProfile();

			if (applicationProfile != null) {

				if (applicationProfile.getAppUser() != null) {

					if (log.isInfoEnabled()) {


						log.info("App User is {}", applicationProfile.getAppUser());
					}
					activity.setAppUser(applicationProfile.getAppUser());
				}
			}

			TransactionProfileType transactionProfile = payloadContext.getTransactionProfile();

			if (transactionProfile != null) {

				if (transactionProfile.getEnvironment() != null) {

					if (log.isInfoEnabled()) {


						log.info("Environment is {}", transactionProfile.getEnvironment());
					}
					activity.setEnvironment(transactionProfile.getEnvironment().toString());
				}
			}

			if (transactionProfile != null) {

				if (transactionProfile.getGlobalTransactionID() != null) {

					if (log.isInfoEnabled()) {


						log.info("Global transaction ID is {}", transactionProfile.getGlobalTransactionID());
					}

					activity.setGlobalTransactionId(transactionProfile.getGlobalTransactionID());
				}
			}

			if (transactionProfile != null) {

				if (transactionProfile.getTransactionDateTime() != null) {

					if (log.isInfoEnabled()) {


						log.info("Transaction date time is {}", transactionProfile.getTransactionDateTime());
					}

					activity.setDateTime(transactionProfile.getTransactionDateTime().getValue());
				} else {

					log.info(
							"As the date time is coming null or empty from the request body, hence setting date time as current time");

					DateTime dateTime = ActmonClientUtil.processDefaultDate();

					activity.setDateTime(dateTime);
				}
			}

			if (applicationProfile != null) {

				if (applicationProfile.getAppName() != null) {

					if (log.isInfoEnabled()) {


						log.info("App Name is {}", applicationProfile.getAppName());
					}
					activity.setAppName(applicationProfile.getAppName());
				}
			}

		}

		return activity;
	}
	
	private String getActmonFlow() {

		String actmonFlow = Constants.RABBIT_FLOW;

		if ("true".equalsIgnoreCase(segmentFlowCheckEnabled)) {
			if ("true".equalsIgnoreCase(kafkaFlowEnabled)) {
				actmonFlow = Constants.KAFKA_FLOW;
			}
		} else {
			String appFlow = actmonLoggingProperties.getActmonFlow();

			if (!ObjectUtils.isEmpty(appFlow)) {
				List<String> flowList = new ArrayList<>();
				flowList.add(Constants.KAFKA_FLOW);
				flowList.add(Constants.RABBIT_FLOW);

				if (flowList.contains(appFlow)) {
					actmonFlow = appFlow;
				}
			} else {
				if ("true".equalsIgnoreCase(kafkaFlowEnabled)) {
					actmonFlow = Constants.KAFKA_FLOW;
				}
			}
		}

		return actmonFlow;
	}

	/**
	 * Attribute determines whether an exception is thrown when a failure to record
	 * the activity event occurs. If false, the event is logged. Default FALSE.
	 *
	 * @param failOnActivityMonitorFailure the failOnActivityMonitorFailure to set
	 */
	public void setFailOnActivityMonitorFailure(boolean failOnActivityMonitorFailure) {
		this.failOnActivityMonitorFailure = failOnActivityMonitorFailure;
	}

	/**
	 * A Map of header SpEL expressions (header name->expression). Intended for use
	 * on an abstract bean, with the concrete bean using
	 * {@link #setHeaderExpressions(Map)} for additional headers.
	 *
	 * @param headerExpressions
	 */
	public void setBaseHeaderExpressions(Map<String, String> headerExpressions) {
		setHeaderExpressions(headerExpressions);
	}

	/**
	 * A Map of event activity attribute SpEL expressions (createEventRequest
	 * attribute->expression). Attributes can be fully qualified
	 * ('CreateEventRequest.PayloadContext.MessageProfile.Domain') or unqualified,
	 * in which case they are assumed to be on
	 * 'CreateEventRequest.Document.EventActivity'. Intended for use on an abstract
	 * bean, with the concrete bean using {@link #setActivityExpressions(Map)} for
	 * additional attributes.
	 *
	 * @param activityExpressions
	 */
	public void setBaseActivityExpressions(Map<String, String> activityExpressions) {
		setActivityExpressions(activityExpressions);
	}

	/**
	 * A Map of event activity attribute values (createEventRequest
	 * attribute->value). Attributes can be fully qualified
	 * ('CreateEventRequest.PayloadContext.MessageProfile.Domain') or unqualified,
	 * in which case they are assumed to be on
	 * 'CreateEventRequest.Document.EventActivity'. Intended for use on an abstract
	 * bean, with the concrete bean using {@link #setActivityAttributes(Map)} for
	 * additional attributes.
	 *
	 * @param activityAttributes
	 */
	public void setBaseActivityAttributes(Map<String, String> activityAttributes) {
		setActivityAttributes(activityAttributes);
	}

	/**
	 * A map of name/value pairs to add to the activity event in addition to the
	 * message headers. Intended for use on an abstract bean, with the concrete bean
	 * using {@link #setNameValuePairs(Map)} for additional properties.
	 *
	 * @param nameValuePairs the nameValuePairs to set
	 */
	public void setBaseNameValuePairs(Map<String, String> nameValuePairs) {
		this.nameValuePairs.putAll(nameValuePairs);
	}

	/**
	 * A Map of header SpEL expressions (header name->expression).
	 *
	 * @param headerExpressions
	 * @see #setBaseHeaderExpressions(Map)
	 */
	public void setHeaderExpressions(Map<String, String> headerExpressions) {
		for (String header : headerExpressions.keySet()) {
			this.headerProcessors.put(header, new ExpressionEvaluatingMessageProcessor<>(
					new SpelExpressionParser().parseExpression(headerExpressions.get(header)), String.class));
		}
	}

	/**
	 * A Map of event activity attribute SpEL expressions (createEventRequest
	 * attribute->expression). Attributes can be fully qualified
	 * ('CreateEventRequest.PayloadContext.MessageProfile.Domain') or unqualified,
	 * in which case they are assumed to be on
	 * 'CreateEventRequest.Document.EventActivity'.
	 *
	 * @param activityExpressions
	 * @see #setBaseActivityExpressions(Map)
	 */
	public void setActivityExpressions(Map<String, String> activityExpressions) {
		BeanWrapper beanWrapper = getBeanWrapper(new CreateEventRequest());
		for (String activityField : activityExpressions.keySet()) {
			this.activityProcessors.put(extendNameIfNecessary(beanWrapper, activityField),
					new ExpressionEvaluatingMessageProcessor<>(
							new SpelExpressionParser().parseExpression(activityExpressions.get(activityField)),
							String.class));
		}
	}

	/**
	 * A Map of event activity attribute values (createEventRequest
	 * attribute->value). Attributes can be fully qualified
	 * ('CreateEventRequest.PayloadContext.MessageProfile.Domain') or unqualified,
	 * in which case they are assumed to be on
	 * 'CreateEventRequest.Document.EventActivity'.
	 *
	 * @param activityAttributes
	 * @see #setBaseActivityAttributes(Map)
	 */
	public void setActivityAttributes(Map<String, String> activityAttributes) {
		BeanWrapper beanWrapper = getBeanWrapper(new CreateEventRequest());
		for (String activityField : activityAttributes.keySet()) {
			this.activityAttributes.put(extendNameIfNecessary(beanWrapper, activityField),
					activityAttributes.get(activityField));
		}
	}

	/**
	 * A map of name/value pairs to add to the activity event in addition to the
	 * message headers.
	 *
	 * @param nameValuePairs the nameValuePairs to set
	 */
	public void setNameValuePairs(Map<String, String> nameValuePairs) {
		this.nameValuePairs.putAll(nameValuePairs);
	}
	
	
	public static interface TransactionalSenderNoFlowControl {
		// @Transactional(propagation = Propagation.REQUIRES_NEW, value =
		// "activityMonitorJmsTransactionManager")
		void sendActivity(String activity);
	}

	/**
	 * Starts a transaction and sends to a gateway that ultimately sends to RabbitMQ
	 * via AMQO. This class is kept for backward compatibility.
	 *
	 * <AUTHOR> Russell
	 */
	public static class TransactionalSenderNoFlowControlImpl implements TransactionalSenderNoFlowControl {

		private final ActivityOutGateway outGateway;

		// private final ConnectionFactory connectionFactory;

		public TransactionalSenderNoFlowControlImpl(ActivityOutGateway outGateway) {
			this.outGateway = outGateway;
			// this.connectionFactory = connectionFactory;
		}

		public void sendActivity(String activity) {
			// this.disableSonicFlowControl(this.connectionFactory);
			this.outGateway.sendActivity(activity);
		}
	}

	/**
	 * {@inheritDoc}
	 * <p>
	 * Required to allow injection of {@code BeanFactory} into
	 * {@link #headerProcessors} and {@link #activityProcessors}.
	 * </p>
	 */
	@Override
	public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
		this.beanFactory = beanFactory;
	}

	/**
	 * {@inheritDoc}
	 * <p>
	 * Set {@code beanFactory} on each item in {@link #headerProcessors} and
	 * {@link #activityProcessors}. A {@code BeanFactory} implementation is required
	 * for an {@code ExpressionEvaluatingMessageProcessor} to evaluate SpEL
	 * expressions.
	 * </p>
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		for (ExpressionEvaluatingMessageProcessor<String> headerProcessor : headerProcessors.values()) {
			headerProcessor.setBeanFactory(this.beanFactory);
		}

		for (ExpressionEvaluatingMessageProcessor<String> activityProcessor : activityProcessors.values()) {
			activityProcessor.setBeanFactory(this.beanFactory);
		}
	}

}

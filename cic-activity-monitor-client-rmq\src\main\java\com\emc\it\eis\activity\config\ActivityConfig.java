/**
 * 
 */
package com.emc.it.eis.activity.config;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.LongSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import com.emc.it.eis.activity.config.util.ConfigUtil;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class ActivityConfig {

	@Value("${spring.kafka.bootstrap-servers}")
	private String bootstrapServers;

	@Value("${spring.kafka.producer.acks}")
	private String acks;

	@Value("${spring.kafka.ssl.protocol}")
	private String protocol;

	@Value("${spring.kafka.properties.security.protocol}")
	private String securityProtocol;

	@Value("${spring.kafka.properties.sasl.mechanism}")
	private String mechanism;

	@Value("${spring.kafka.ssl.trust-store-type}")
	private String trustStoreType;

	@Value("${spring.kafka.ssl.truststore-location}")	
	private String trustStoreLocation;

	@Value("${spring.kafka.ssl.trust-store-password}")
	private String trustStorePassword;

	@Value("${max.block.ms}")
	private String maxBlockMS;

	@Value("${reconnect.backoff.ms}")
	private String reconnectBackOffMS;

	@Value("${max.request.size.bytes}")
	private String maxRequestSize;

	@Value("${linger.ms.config}")
	private String lingerMsConfig;

	@Value("${batch.size.config}")
	private String batchSizeConfig;

	@Value("${retry.count}")
	private String producerRetry;

	@Value("${reconnect.backoff.max.ms}")
	private String reconnectBackOffMaxMs;

	@Value("${reconnect.backoff.ms}")
	private String reconnectBackOffMs;

	@Value("${retry.backoff.ms}")
	private String retryBackOffMs;

	@Value("${buffer.memory.config}")
	private String bufferMemoryConfig;

	@Value("${max.in.flight.req.per.connection}")
	private String maxInFlightReqPerConnection;

	@Value("${request.time.out.ms.config}")
	private String requestTimeOutMSConfig;
	
	@Value("${actmon.gitlab.truststore.loc}")
	private String actmonGitlabTruststoreLoc;
	
	@Value("${defined.truststore.location}")	
	private String definedTrustStoreLocation;
	
	@Value("${spring.kafka.properties.ldap.sasl.mechanism}")	
	private String ldapSaslMechanism;	
	
	@Value("${ldap.sasl.jaas.config}")	
	private String ldapSaslJassConfig;
	
	@Value("${ldap.secret}")	
	private String ldapSecret;
	
	@Value("${default.trustStoreLocation:/home/<USER>/app/WEB-INF/classes/tls/actmon/kafka/kafka.client.truststore.jks}")	
	private String defaultTrustStoreLocation;
	
	@Autowired
	private ConfigUtil configUtil;

	// https://docs.confluent.io/current/installation/configuration/producer-configs.html
	// https://www.cloudera.com/documentation/kafka/latest/topics/kafka_performance.html

	@Bean
	public Map<String, Object> producerConfigs() {

		Map<String, Object> props = new HashMap<>();
		props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
		props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, LongSerializer.class);
		props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
		props.put(ProducerConfig.ACKS_CONFIG, acks);
		props.put("max.block.ms", maxBlockMS);
		props.put("reconnect.backoff.ms", reconnectBackOffMs);
		props.put("reconnect.backoff.max.ms", reconnectBackOffMaxMs);
		props.put("retry.backoff.ms", retryBackOffMs);

		props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, maxRequestSize);
		props.put(ProducerConfig.RETRIES_CONFIG, producerRetry);
		props.put(ProducerConfig.LINGER_MS_CONFIG, lingerMsConfig);
		props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSizeConfig);
		props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemoryConfig);
		props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, maxInFlightReqPerConnection);
		props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, requestTimeOutMSConfig);

		// security Configuration :

		props.put("security.protocol", securityProtocol); // Implement SASL LDAP authentication and TLS encryption
															// in Kafka client
		props.put("sasl.mechanism", ldapSaslMechanism); // Explicitly set SASL mechanism to PLAIN - LDAP
	
		props.put("ssl.enabled.protocols", protocol); // Set TLS protocol to v1.2
		try {
			//replace the truststore file absolute path
			if(!trustStoreLocation.equals(definedTrustStoreLocation))
			{
				log.info("trustStoreLocation is getting set from GitLab location={}", actmonGitlabTruststoreLoc);
				if(trustStoreLocation.contains("trustStoreFileAbsolutePath"))
				{
					trustStoreLocation = trustStoreLocation.replace("trustStoreFileAbsolutePath", configUtil.tempFileConfigPath(actmonGitlabTruststoreLoc, ".jks"));
				}
				else
				{
					log.error("trustStoreFileAbsolutePath string not found in {}", trustStoreLocation);
				}
			}
			else
			{
				log.info("trustStoreLocation is getting set from ClassPathResource={}", definedTrustStoreLocation);
				definedTrustStoreLocation = StringUtils.substringAfter(definedTrustStoreLocation, ConfigUtil.CLASS_PATH_SEPERATOR);
				log.info("definedTrustStoreLocation after classpath seperator ={}", definedTrustStoreLocation);
				Resource resouce = new ClassPathResource(definedTrustStoreLocation);
				InputStream inputStream = resouce.getInputStream();
				try {					
					File tmpFile = File.createTempFile("kafka.client.truststore", ".jks");
					FileUtils.copyInputStreamToFile(inputStream, tmpFile);
					trustStoreLocation = tmpFile.getAbsolutePath();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					log.error("got error in ActivityConfig::producerConfigs - {}", e.getMessage(), e);
					log.warn("setting trustStoreLocation to default path - {}", defaultTrustStoreLocation);
					trustStoreLocation = defaultTrustStoreLocation;
				}
				finally {
				    IOUtils.closeQuietly(inputStream);
				}
			}
			log.info("trustStoreLocation is getting set with value={}", trustStoreLocation);
			//append  the ldap-secret to ldapSaslJassConfig
			ldapSaslJassConfig = ldapSaslJassConfig.concat(" password=" + "\"" + ldapSecret + "\"" + ";")	;
		} catch (Throwable e) {
			// TODO Auto-generated catch block		
			log.error("got error in ActivityConfig::producerConfigs - {}", e.getMessage(), e);
		} 
		// pass Truststore or Signer certifcate store
		props.put("ssl.truststore.password", trustStorePassword); // trustrore password
		props.put("ssl.truststore.type", trustStoreType);
		props.put("ssl.truststore.location", trustStoreLocation);
		
		props.put("sasl.jaas.config", ldapSaslJassConfig);

		return props;
	}

	@Bean
	public ProducerFactory<Long, Map<String, Object>> producerFactory() {
		return new DefaultKafkaProducerFactory<>(producerConfigs());
	}

	@Bean
	public KafkaTemplate<Long, Map<String, Object>> kafkaTemplate() {
		return new KafkaTemplate<>(producerFactory());
	}	
	
	/*public static void main(String[] args) throws Exception {
		ConfigUtil configUtil = new ConfigUtil();
		String saslJaasConfig = "com.sun.security.auth.module.Krb5LoginModule required useKeyTab=true storeKey=true keyTab=\"keytabFileAbsolutePath\" principal=\"<EMAIL>\"";
		saslJaasConfig = saslJaasConfig.replace("keytabFileAbsolutePath", configUtil.tempKeytabConfigPath());
		System.out.println("saslJaasConfig="+saslJaasConfig);
		
		String definedTrustStoreLocation = "classpath:tls/actmon/kafka/kafka.client.truststore.jks";
		
		definedTrustStoreLocation = StringUtils.substringAfter(definedTrustStoreLocation, "classpath:");
		
		System.out.println("definedTrustStoreLocation="+definedTrustStoreLocation);
		
		Resource resouce = new ClassPathResource(definedTrustStoreLocation);
		String trustStoreLocation = resouce.getFile().getAbsolutePath();
		
		System.out.println("trustStoreLocation="+trustStoreLocation);
	}*/

}

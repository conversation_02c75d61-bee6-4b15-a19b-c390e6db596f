<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util"
	xmlns:int="http://www.springframework.org/schema/integration"
	xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:component-scan base-package="com.emc.it.eis.activity.restclient"/>

	<import resource="classpath:META-INF/spring/cic-activity-monitor-client/activity-monitor-common-beans.xml" />
	<import resource="classpath:META-INF/spring/cic-config/cic-common-activity-monitor-bean-config.xml" />
	<import resource="classpath:META-INF/spring/cic-activity-monitor-client/activity-monitor-flows.xml" />
	<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-client-beans.xml"/>
	<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-beans.xml" />
	
<!--	<bean id="activityMonitorReloadableProperties" class="com.emc.it.eis.activity.ReloadablePropertiesImpl" />-->

	<bean id="baseActivityMonitorInterceptor" abstract="true" class="com.emc.it.eis.activity.restclient.ActivityInterceptorREST">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg ref="cic.activity.monitor.wsclient.transactionalActivitySender" />
		<constructor-arg>
			<map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
			</map>
		</constructor-arg>
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
	</bean>

	<bean id="basePayloadActivityMonitorInterceptor" abstract="true" class="com.emc.it.eis.activity.restclient.ActivityInterceptorREST">
		<constructor-arg ref="eventMarshaller" />
		<constructor-arg ref="cic.activity.monitor.wsclient.transactionalActivitySender" />
		<constructor-arg>
			<map>
				<entry key="PayloadContext.MessageProfile.Domain" value="'ERP'" />
				<entry key="Payload" value="payload" />
			</map>
		</constructor-arg>
<!--		<constructor-arg ref="activityMonitorReloadableProperties" />-->
	</bean>

	<bean id="cic.activity.monitor.wsclient.transactionalActivitySender" class="com.emc.it.eis.activity.restclient.ActivityInterceptorREST$TransactionalActivitySenderImpl" />
		
	<bean id="eventMarshaller" class="org.springframework.oxm.jaxb.Jaxb2Marshaller">
		<property name="contextPath" value="com.emc.it.enterprise.data.v1" />
	</bean>
	
	
	<beans profile="test">
		<context:component-scan base-package="com.emc.it.eis.activity.restclient"/>
		<import resource="classpath:META-INF/spring/cic-activity-monitor-client/activity-monitor-common-beans.xml" />
		<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-client-beans.xml"/>
		<import resource="classpath:/META-INF/spring/cic-config/cic-common-properties-config.xml"/>
	    <!-- <util:properties id="cic.application.properties" location="classpath:cic-activity-monitor-client.properties">
	        <prop key="application.configuration.name">flow-test-app</prop>
	        <prop key="cic.process.id">flow-test-process</prop>
	        <prop key="am.environment">dev</prop>
	        <prop key="am.serviceName">service</prop>
	        <prop key="cic.actmon.host">localhost</prop>
	        <prop key="cic.actmon.port">8090</prop>
	        <prop key="cic.actmon.context.path">cic-activity-monitor-webservice/actmon.ws</prop>
	        <prop key="cic.actmon.ws.available">true</prop>
	        
	    </util:properties> -->
	    
	    <bean id="httpServer" class="com.emc.it.eis.common.http.HttpServerFactory">
			<constructor-arg name="port" value="${cic.actmon.port}" />
			<constructor-arg name="contextBindings">
				<map>
					<entry key="/cic-activity-monitor-webservice/actmon.ws" value-ref="cicActmonWSResponseHandler" />
				</map>
			</constructor-arg>
		</bean>
		
		<bean id="httpServer1" class="com.emc.it.eis.common.http.HttpServerFactory">
			<constructor-arg name="port" value="${cic.actmon.port1}" />
			<constructor-arg name="contextBindings">
				<map>
					<entry key="/cic-activity-monitor-webservice/actmon.ws" value-ref="cicActmonWSResponseHandler" />
				</map>
			</constructor-arg>
		</bean>
		<bean id="cicActmonWSResponseHandler" class="com.emc.it.eis.common.ws.MockSOAPResponseHandler">
			<constructor-arg value="classpath:data/cic-ws-response.xml" />
		</bean>
	</beans>
	
	<beans profile="standalone">
		<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-beans.xml" />
		<import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-client-beans.xml"/>
		<import resource="classpath:META-INF/spring/cic-config/cic-common-activity-monitor-bean-config.xml" />
		
		<!-- <bean id="httpServer" class="com.emc.it.eis.common.http.HttpServerFactory">
			<constructor-arg name="port" value="${cic.actmon.port}" />
			<constructor-arg name="contextBindings">
				<map>
					<entry key="/cic-activity-monitor-webservice/actmon.ws" value-ref="cicActmonWSResponseHandler" />
				</map>
			</constructor-arg>
		</bean> -->
		
		<bean id="cicActmonWSResponseHandler" class="com.emc.it.eis.common.ws.MockSOAPResponseHandler">
			<constructor-arg value="classpath:data/cic-ws-response.xml" />
		</bean>
	</beans>
	
	
</beans>
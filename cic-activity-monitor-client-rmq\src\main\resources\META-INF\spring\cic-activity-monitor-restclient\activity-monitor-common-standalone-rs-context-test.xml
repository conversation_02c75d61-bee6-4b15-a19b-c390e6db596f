<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:int="http://www.springframework.org/schema/integration"
       xsi:schemaLocation="http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.emc.it.eis.activity.restclient"/>

    <import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-beans.xml"/>
    <import resource="classpath:META-INF/spring/cic-activity-monitor-restclient/activity-monitor-common-rs-client-beans.xml"/>

    <bean id="httpServer" class="com.emc.it.eis.common.http.HttpServerFactory" scope="prototype">
        <constructor-arg name="port" value="${cic.actmon.port}"/>
        <constructor-arg name="contextBindings">
            <map>
                <entry key="/${cic.actmon.context.path}" value-ref="cicActmonWSResponseHandler"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="cicActmonWSResponseHandler" class="com.emc.it.eis.common.ws.MockSOAPResponseHandler">
        <constructor-arg value="classpath:data/cic-ws-test-response.xml"/>
    </bean>

</beans>
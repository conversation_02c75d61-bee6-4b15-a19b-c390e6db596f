package com.emc.it.eis.activity.restclient;

import java.io.StringWriter;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.BeansException;
//import org.springframework.cloud.util.EnvironmentAccessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.emc.it.eis.activity.CreateEventRequestValidator;
import com.emc.it.eis.activity.Status;
import com.emc.it.eis.activity.exception.RequestValidationException;
import com.emc.it.enterprise.data.v1.CreateEventRequest;
import com.emc.it.enterprise.data.v1.CreateEventRequest.Document.EventActivity.NameValuePairs.NameValuePair;
import com.emc.it.enterprise.data.v1.DateTimeType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaMapper;

/**
 * 
 * <AUTHOR>
 * @version $Id$
 */
public abstract class BaseActmonLoggerImpl implements ApplicationContextAware {
	//private EnvironmentAccessor environment = new EnvironmentAccessor();
	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final ObjectMapper objectMapper = new JodaMapper().configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
			true);
	protected static ActmonEventBean activityBean;
	protected static ApplicationContext applicationContext;
	protected Class<?> clazz;

	public void setApplicationContext(ApplicationContext appContext) throws BeansException {
		applicationContext = appContext;
	}

	/**
	 * Base method responsible to log a activity monitor transaction.
	 * 
	 * @param status
	 * @param bid
	 * @param altBid
	 * @param gti
	 * @param step
	 * @param payload
	 * @param detail
	 * @param summary
	 * @param eventCode
	 */
	protected void logEvent(String status, String bid, String altBid, String gti, String step, Object payload,
			Object detail, String summary, String eventCode, Map<String, Object> properties) {
		String className = clazz.getName();
		logger.info("Caller class, {}", className);

		Assert.notNull(bid, "Business identifier is mandatory attribute");
		ResponseEntity<ResponseEntity<HttpStatus>> responseEntity = null;

		try {
			logger.info("Preparing CreateEventRequest....");
			CreateEventRequest createEventRequest = prepareCreateEventRequest(status, bid, altBid, gti, step, payload,
					detail, summary, eventCode, properties);
			MultiValueMap<String, Object> headers = new LinkedMultiValueMap<>();
			headers.add("Content-Type", "application/json");
			/*
			 * Validate the request
			 */
			try {
				CreateEventRequestValidator.validate(createEventRequest);
			} catch (RequestValidationException e) {
				logger.error("Failed to validate activity event: {}", objectToJson(createEventRequest), e);
			}
			Map<String, Boolean> activityLoggingAttributes = activityBean.getActivityLoggingAttributes();
			if (activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_LOGGING_ENABLED)) {
				if (checkActmonLogginEnabled(createEventRequest, activityLoggingAttributes)) {
					responseEntity = doCallWS(objectToJson(createEventRequest), "", headers);
				}
			} else {
				if (logger.isDebugEnabled()) {

					logger.debug("Actmon is disabled for the component: {}", createEventRequest.getPayloadContext().getMessageProfile().getServiceName());
				}
			}

		} catch (Exception e) {
			logger.error("Exception ", e);
			logger.error("{}", "Response data on error :" + responseEntity != null ? responseEntity.getStatusCode()
			: HttpStatus.NOT_FOUND);
			logger.error(
					"******************************************************************************************************************");
		}
	}

	public boolean checkActmonLogginEnabled(CreateEventRequest createEventRequest,
			Map<String, Boolean> activityLoggingAttributes) {
		String status = createEventRequest.getDocument().getEventActivity().getStatus().getValue();
		return !((status.equals(Status.STARTED.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_START_LOGGING_ENABLED))
		|| (status.equals(Status.COMPLETED.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_COMPLETED_LOGGING_ENABLED))
		|| (status.equals(Status.TERMINATED.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_TERMINATED_LOGGING_ENABLED))
		|| (status.equals(Status.FAILED.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_FAILED_LOGGING_ENABLED))
		|| (status.equals(Status.INFO.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_INFO_LOGGING_ENABLED))
		|| (status.equals(Status.WARNING.name())
		&& !activityLoggingAttributes.get(ActivityLoggingConstants.ACTMON_WARN_LOGGING_ENABLED)));
	}

	/**
	 * Use a bean wrapper to populate the event request; will create subordinate
	 * objects.
	 * 
	 * @param createEventRequest
	 * @return the BeanWrapper
	 */
	private BeanWrapper getBeanWrapper(CreateEventRequest createEventRequest) {
		BeanWrapper beanWrapper = new BeanWrapperImpl(createEventRequest);
		beanWrapper.setAutoGrowNestedPaths(true);
		return beanWrapper;
	}

	/**
	 * 
	 * This method will create <code>CreateEventRequest</code> object.
	 * 
	 * @param status
	 * @param bid
	 * @param altBid
	 * @param gti
	 * @param step
	 * @param summary
	 * @param payload
	 * @param detail
	 * @param eventCode
	 * @param properties
	 * @return CreateEventRequest object
	 */
	public CreateEventRequest prepareCreateEventRequest(String status, String bid, String altBid, String gti,
			String step, Object payload, Object detail, String summary, String eventCode,
			Map<String, Object> properties) {
		CreateEventRequest createEventRequest = new CreateEventRequest();
		BeanWrapper beanWrapper = getBeanWrapper(createEventRequest);
		logger.info("Prepare payloadcontext");
		preparePayloadContext(beanWrapper, gti);

		logger.info("Prepare document..");
		prepareDocument(beanWrapper, status, bid, altBid, step, payload, detail, summary, eventCode, properties);

		return createEventRequest;
	}

	/**
	 * 
	 * @param beanWrapper
	 * @param gti
	 * @return
	 */
	private void preparePayloadContext(BeanWrapper beanWrapper, String gti) {
		DateTimeType now = new DateTimeType();
		now.setValue(new DateTime());
		setActivityAttribute(beanWrapper, "PayloadContext.TransactionProfile.TransactionDateTime", now);
		setActivityAttribute(beanWrapper, "PayloadContext.TransactionProfile.GlobalTransactionID", processGTI(gti));
		setExpressions(activityBean.getHeaderAttributes(), beanWrapper);
	}

	/**
	 * @param object
	 * @return
	 */
	public String objectToJson(Object object) {
		try {
			StringWriter writer = new StringWriter();
			this.objectMapper.writeValue(writer, object);
			return writer.toString();
		} catch (Exception e) {
			logger.error("Could not transform object to json({}):", object, e);
			return "Could not transform non-string object to json";
		}
	}

	private void prepareDocument(BeanWrapper beanWrapper, String status, String bid, String altBid, String step,
			Object payload, Object detail, String summary, String eventCode, Map<String, Object> properties) {
		prepareEventActivity(beanWrapper, status, bid, altBid, step, payload, detail, summary, eventCode, properties);
	}

	/**
	 * This method set activity expressions
	 * 
	 * @param activityExpressions
	 * @param beanWrapper
	 */
	private void setExpressions(Map<String, String> expressions, BeanWrapper beanWrapper) {
		for (String activityField : expressions.keySet()) {
			setActivityAttribute(beanWrapper, activityField, expressions.get(activityField));
		}
	}

	/**
	 * This method will set each attribute to bean wrapper
	 * 
	 * @param beanWrapper
	 * @param activityComponent
	 * @param attributeValue
	 */
	private void setActivityAttribute(BeanWrapper beanWrapper, String activityComponent, Object attributeValue) {
		beanWrapper.setPropertyValue(activityComponent, attributeValue);
	}

	private void prepareEventActivity(BeanWrapper beanWrapper, String status, String bid, String altBid, String step,
			Object payload, Object detail, String summary, String eventCode, Map<String, Object> properties) {
		setActivityAttribute(beanWrapper, "Document.EventActivity.Step.Value", step);
		setActivityAttribute(beanWrapper, "Document.EventActivity.Status.Value", status);
		setActivityAttribute(beanWrapper, "Document.EventActivity.Summary.Value", summary);
		setActivityAttribute(beanWrapper, "Document.EventActivity.Detail.value", processDetail(detail));
		setActivityAttribute(beanWrapper, "Document.EventActivity.Payload", payload + "");
		setActivityAttribute(beanWrapper, "Document.EventActivity.BusinessIdentifier", bid);
		setActivityAttribute(beanWrapper, "Document.EventActivity.AlternateBusinessIdentifier", altBid);
		setActivityAttribute(beanWrapper, "Document.EventActivity.EventCode", eventCode);
		processHostThreadValue(beanWrapper);
		setExpressions(activityBean.getActivityAttributes(), beanWrapper);

		if (properties == null) {
			properties = new HashMap<>();
		}
		properties.put("PayloadContext.MessageProfile.Process",
				beanWrapper.getPropertyValue("PayloadContext.MessageProfile.Process"));
		properties.put("timestamp", System.currentTimeMillis());
		properties.put("id", beanWrapper.getPropertyValue("PayloadContext.TransactionProfile.GlobalTransactionID"));
		properties.put("EMC.applicationId", beanWrapper.getPropertyValue("PayloadContext.ApplicationProfile.AppName"));
		properties.put("EMC.globalTransactionId",
				beanWrapper.getPropertyValue("PayloadContext.TransactionProfile.GlobalTransactionID"));
		properties.put("EMC.businessIdentifier", bid);

		logger.info("Now processing name value pair");
		processNameValuePairs(beanWrapper, properties);
	}

	private void processNameValuePairs(BeanWrapper beanWrapper, Map<String, Object> props) {
		@SuppressWarnings("unchecked")
		List<NameValuePair> nameValuePairList = (List<NameValuePair>) beanWrapper
				.getPropertyValue("Document.EventActivity.NameValuePairs.NameValuePairs");
		for (String key : props.keySet()) {
			NameValuePair nvp = new NameValuePair();
			nvp.setName(key);
			if (props.get(key) != null) {
				nvp.setValue(props.get(key).toString());
			}
			nameValuePairList.add(nvp);
		}
	}

	private void processHostThreadValue(BeanWrapper beanWrapper) {
		String threadID = Thread.currentThread().getName();
		setActivityAttribute(beanWrapper, "Document.EventActivity.HostName", getHostName());
		setActivityAttribute(beanWrapper, "Document.EventActivity.ThreadID", threadID);
	}

	/**
	 * Actual rest web service call. This method is used from both stand alone(non
	 * spring integration) application and spring integration based application.
	 * 
	 * @param activityEvent
	 * @param operation
	 * @param headers
	 * @return ResponseEntity having status of http service call.
	 */
	@SuppressWarnings("rawtypes")
	public ResponseEntity<ResponseEntity<HttpStatus>> doCallWS(final Object activityEvent, final String operation,
			MultiValueMap<String, Object> headers) {
		ResponseEntity<ResponseEntity<HttpStatus>> responseEntity = null;
		Class<ResponseEntity<HttpStatus>> responseType = null;
		logger.info("Calling actmon rest service !!!!");
		// On any exception suppress exception here, don't throw to caller method/class
		// so that execution can continue
		// without breaking breaking flow.
		try {
			ActmonRestClient restClient = applicationContext.getBean(ActmonRestClient.class);
			if (restClient.getClientProperties().getWsAvailable()) {
				RestTemplate restTemplate = restClient.getRestTemplate();
				URI uri = new URI(restClient.createUrl(operation));
				if (restClient.getApiKey() != null && restClient.getApiKey().length() > 0) {
					headers.add("apikey", restClient.getApiKey());
				}
				@SuppressWarnings("unchecked")
				HttpEntity<CreateEventRequest> httpEntity = new HttpEntity(activityEvent, headers);
				responseEntity = restTemplate.postForEntity(uri, httpEntity, responseType);
				logger.info("Response data  :{}", responseEntity != null ? responseEntity.getStatusCode() : HttpStatus.NOT_FOUND);
			} else {
				logger.warn("Web service is not available this this moment!! Just write activity in Log file");
				logger.warn("Activity in Data : {}", activityEvent);
			}
		} catch (Exception e) {
			logger.error("Exception ", e);
			logger.warn(
					"******************************************************************************************************************");
			logger.warn("Response data on error :{}", responseEntity != null ? responseEntity.getStatusCode() : HttpStatus.NOT_FOUND);
			logger.warn(
					"******************************************************************************************************************");
		}

		if (responseEntity == null) {
			responseEntity = new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}
		return responseEntity;
	}

	/**
	 * This method process <code>detail</code> based on object type.
	 * 
	 * @param detail
	 * @return String
	 */
	private String processDetail(Object detail) {
		if (detail != null) {
			if (detail instanceof String) {
				return detail.toString();
			} else if (detail instanceof Throwable throwable) {
				return throwable.getMessage();
			} else {
				return detail.getClass().toString();
			}
		}
		return "";
	}

	/**
	 * This method process <code>global transaction id</code> field.
	 * <p>
	 * If gti value is not provided then this method will generate a random
	 * <code>UUID</code> and will assign to GTI.
	 * </p>
	 * 
	 * @param jsonString
	 * @param gti
	 */
	private String processGTI(String gti) {
		if (StringUtils.isNotBlank(gti)) {
			return gti;
		} else {
			return UUID.randomUUID().toString();
		}
	}

	/**
	 * Collect host name
	 * 
	 * @return String
	 */
	private String getHostName() {
		String hostName = "";
		try {
			//String servicesString = this.environment.getEnvValue("VCAP_APPLICATION");
			logger.debug("checking for PCF VCAP_APPLICATION...");
/*			if (servicesString != null && !"".equals(servicesString)) {
				logger.debug("Get details from VCAP_APPLICATION on PCF");
				hostName = ActmonClientUtil.getCFHost(servicesString);
			} else {*/
				logger.debug("Get details from host name from TC");
				InetAddress localhost = InetAddress.getLocalHost();
				hostName = localhost.getHostName();
	/*		}*/
		} catch (UnknownHostException e) {
			logger.error("Failed to get hostname", e);
			hostName = "unknown";
		}
		return hostName;
	}

	@SuppressWarnings("static-access")
	public void setActivityBean(ActmonEventBean activityBean) {
		this.activityBean = activityBean;
	}
}

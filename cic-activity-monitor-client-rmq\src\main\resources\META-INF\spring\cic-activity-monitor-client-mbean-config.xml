<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
                           http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="actmonLoggingexporter" class="org.springframework.jmx.export.MBeanExporter" p:autodetect="true"
          p:assembler-ref="assembler" p:namingStrategy-ref="namingStrategy"/>
          
    <bean id="namingStrategy" class="org.springframework.jmx.export.naming.MetadataNamingStrategy"
          p:attributeSource-ref="attributeSource"/>

    <bean id="attributeSource" class="org.springframework.jmx.export.annotation.AnnotationJmxAttributeSource"/>

    <bean id="assembler" class="org.springframework.jmx.export.assembler.MetadataMBeanInfoAssembler"
          p:attributeSource-ref="attributeSource"/>

</beans>
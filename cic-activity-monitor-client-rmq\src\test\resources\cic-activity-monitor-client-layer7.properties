#
#cic activity monitor client configuration
#
#used in dev profile
#
#Property Sheet cic-activity-monitor
#
#Common properties required for activity monitor 
#
cic.activity.monitor.amqp.fanout.exchange.name=AIC.ACTMON.EVENT.DIR.EXG
cic.activity.monitor.amqp.fanout.queue.routing.key=AIC.ACTMON.EVENT.Q
#
#
#Common Rabbit MQ Properties
#
btm.amqp.rmq.username=guest	
btm.amqp.rmq.virtual.host=/	
btm.amqp.rmq.host=msgdevcls03.isus.emc.com	
btm.amqp.rmq.password=guest	
btm.amqp.rmq.port=5672


cic.actmon.host=localhost
cic.actmon.context.path=cic-activity-monitor-webservice/actmon.ws
cic.actmon.ws.available=true
cic.actmon.port1=8099
cic.actmon.http.type=https
cic.actmon.ws.apikey=testKey
cic.actmon.port=8080

# Credentials
cic.rest.webservice.username=aicrestuser
cic.rest.webservice.password=aicrestuser

activity.monitor.ws.so.timeout=30000


spring.security.oauth2.client.registration.dias-creds.provider=dias
spring.security.oauth2.client.registration.dias-creds.client-id=4acab72b-12fe-480a-aafd-6c3e3b852d2c
spring.security.oauth2.client.registration.dias-creds.client-secret=kBQVwb13
spring.security.oauth2.client.registration.dias-creds.authorization-grant-type=client_credentials


spring.security.oauth2.client.provider.dias.token-uri=https://oauth2-api-dev-dci.ausvdc02.pcf.dell.com/oauth2/api/v3/token


aic.oauth2.enabled.feignclient=true
aic.oauth2.enabled.resttemplate=true
aic.oauth2.enabled.webclient=false

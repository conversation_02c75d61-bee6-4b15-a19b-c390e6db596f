package com.emc.it.eis.activity.domain;



import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlID;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement
public class RMQPayload {
	
	@XmlID
	@XmlElement
	private String guid;
	@XmlElement
	private String payload;
	@XmlElement(name="date_time")
	private String dateTime;
	@XmlElement(name="business_identifier")
	private String businessIdentifier;
	@XmlElement(name="app_name")
	private String appName;

}

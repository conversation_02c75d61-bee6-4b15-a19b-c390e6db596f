<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:int-flow="http://www.springframework.org/schema/integration/flow"
	xsi:schemaLocation="http://www.springframework.org/schema/integration/flow http://www.springframework.org/schema/integration/flow/spring-integration-flow.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- Provided flows -->
	<int-flow:flow id="activity-monitor-process-completed-event" />
	<int-flow:flow id="activity-monitor-process-terminated-event" />
	<int-flow:flow id="activity-monitor-process-failed-event" />
	<int-flow:flow id="jms-error-handler" flow-id="cic-default-error-handler">
		<props>
			<prop key="deliveryCountHeaderName">JMSXDeliveryCount</prop>
		</props>
	</int-flow:flow>

</beans>
